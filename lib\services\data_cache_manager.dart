// 바라 부스 매니저 - 데이터 캐시 매니저
// 중복 쿼리 실행 방지를 위한 캐시 시스템

import 'dart:async';
import '../utils/logger_utils.dart';

/// 캐시된 데이터 항목
class CachedDataItem<T> {
  final T data;
  final DateTime cachedAt;
  final Duration ttl;
  
  CachedDataItem({
    required this.data,
    required this.cachedAt,
    required this.ttl,
  });
  
  /// 캐시가 만료되었는지 확인
  bool get isExpired {
    return DateTime.now().difference(cachedAt) > ttl;
  }
  
  /// 남은 TTL 시간
  Duration get remainingTtl {
    final elapsed = DateTime.now().difference(cachedAt);
    return ttl - elapsed;
  }
}

/// 데이터 캐시 매니저
/// 
/// 중복 쿼리 실행을 방지하고 성능을 향상시키기 위한 캐시 시스템
class DataCacheManager {
  static const String _tag = 'DataCacheManager';
  
  static DataCacheManager? _instance;
  static DataCacheManager get instance => _instance ??= DataCacheManager._();
  
  DataCacheManager._();
  
  /// 캐시 저장소
  final Map<String, CachedDataItem> _cache = {};
  
  /// 진행 중인 로딩 작업들 (중복 요청 방지)
  final Map<String, Future> _pendingLoads = {};
  
  /// 기본 TTL (Time To Live)
  static const Duration defaultTtl = Duration(minutes: 5);
  
  /// 워크스페이스별 데이터 TTL
  static const Duration workspaceTtl = Duration(minutes: 10);
  
  /// 상품 데이터 TTL
  static const Duration productTtl = Duration(minutes: 3);
  
  /// 판매 기록 TTL
  static const Duration salesLogTtl = Duration(minutes: 1);
  
  /// 캐시에서 데이터 가져오기
  T? get<T>(String key) {
    final item = _cache[key];
    
    if (item == null) {
      return null;
    }
    
    if (item.isExpired) {
      _cache.remove(key);
      LoggerUtils.logDebug('만료된 캐시 제거: $key', tag: _tag);
      return null;
    }
    
    LoggerUtils.logDebug('캐시 히트: $key (TTL: ${item.remainingTtl.inSeconds}초)', tag: _tag);
    return item.data as T;
  }
  
  /// 캐시에 데이터 저장
  void set<T>(String key, T data, {Duration? ttl}) {
    final effectiveTtl = ttl ?? defaultTtl;
    
    _cache[key] = CachedDataItem<T>(
      data: data,
      cachedAt: DateTime.now(),
      ttl: effectiveTtl,
    );
    
    LoggerUtils.logDebug('캐시 저장: $key (TTL: ${effectiveTtl.inSeconds}초)', tag: _tag);
  }
  
  /// 중복 요청 방지와 함께 데이터 로드
  Future<T> getOrLoad<T>(
    String key,
    Future<T> Function() loader, {
    Duration? ttl,
  }) async {
    // 1. 캐시에서 확인
    final cached = get<T>(key);
    if (cached != null) {
      return cached;
    }
    
    // 2. 이미 로딩 중인지 확인
    if (_pendingLoads.containsKey(key)) {
      LoggerUtils.logDebug('이미 로딩 중 - 대기: $key', tag: _tag);
      return await _pendingLoads[key] as T;
    }
    
    // 3. 새로운 로딩 시작
    LoggerUtils.logDebug('새로운 데이터 로딩 시작: $key', tag: _tag);
    
    final loadingFuture = _performLoad<T>(key, loader, ttl);
    _pendingLoads[key] = loadingFuture;
    
    try {
      final result = await loadingFuture;
      return result;
    } finally {
      _pendingLoads.remove(key);
    }
  }
  
  /// 실제 로딩 수행
  Future<T> _performLoad<T>(
    String key,
    Future<T> Function() loader,
    Duration? ttl,
  ) async {
    try {
      final data = await loader();
      set(key, data, ttl: ttl);
      return data;
    } catch (e) {
      LoggerUtils.logError('데이터 로딩 실패: $key', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 특정 키의 캐시 무효화
  void invalidate(String key) {
    _cache.remove(key);
    LoggerUtils.logDebug('캐시 무효화: $key', tag: _tag);
  }
  
  /// 패턴으로 캐시 무효화
  void invalidatePattern(String pattern) {
    final keysToRemove = _cache.keys.where((key) => key.contains(pattern)).toList();
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
    
    if (keysToRemove.isNotEmpty) {
      LoggerUtils.logDebug('패턴 캐시 무효화: $pattern (${keysToRemove.length}개)', tag: _tag);
    }
  }
  
  /// 워크스페이스별 캐시 무효화
  void invalidateWorkspace(int workspaceId) {
    invalidatePattern('workspace_$workspaceId');
  }
  
  /// 만료된 캐시 정리
  void cleanupExpired() {
    final expiredKeys = _cache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LoggerUtils.logDebug('만료된 캐시 정리: ${expiredKeys.length}개', tag: _tag);
    }
  }
  
  /// 전체 캐시 클리어
  void clearAll() {
    final count = _cache.length;
    _cache.clear();
    _pendingLoads.clear();
    LoggerUtils.logInfo('전체 캐시 클리어: ${count}개', tag: _tag);
  }
  
  /// 캐시 통계 정보
  Map<String, dynamic> getStats() {
    int expiredCount = 0;
    int validCount = 0;

    for (final item in _cache.values) {
      if (item.isExpired) {
        expiredCount++;
      } else {
        validCount++;
      }
    }
    
    return {
      'totalItems': _cache.length,
      'validItems': validCount,
      'expiredItems': expiredCount,
      'pendingLoads': _pendingLoads.length,
    };
  }
  
  /// 주기적 캐시 정리 시작
  Timer? _cleanupTimer;
  
  void startPeriodicCleanup({Duration interval = const Duration(minutes: 5)}) {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(interval, (_) {
      cleanupExpired();
    });
    
    LoggerUtils.logInfo('주기적 캐시 정리 시작 (${interval.inMinutes}분 간격)', tag: _tag);
  }
  
  /// 주기적 캐시 정리 중지
  void stopPeriodicCleanup() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    LoggerUtils.logInfo('주기적 캐시 정리 중지', tag: _tag);
  }
  
  /// 리소스 정리
  void dispose() {
    stopPeriodicCleanup();
    clearAll();
  }

  /// 캐시 키 생성 헬퍼 메서드들

  /// 워크스페이스 데이터 캐시 키
  static String workspaceKey(int workspaceId) => 'workspace_$workspaceId';

  /// 워크스페이스 목록 캐시 키
  static String workspaceListKey() => 'workspace_list';

  /// 상품 목록 캐시 키
  static String productListKey(int workspaceId) => 'workspace_${workspaceId}_products';

  /// 판매 기록 목록 캐시 키
  static String salesLogListKey(int workspaceId) => 'workspace_${workspaceId}_sales_logs';

  /// 카테고리 목록 캐시 키
  static String categoryListKey(int workspaceId) => 'workspace_${workspaceId}_categories';

  /// 판매자 목록 캐시 키
  static String sellerListKey(int workspaceId) => 'workspace_${workspaceId}_sellers';

  /// 선입금 목록 캐시 키
  static String prepaymentListKey(int workspaceId) => 'workspace_${workspaceId}_prepayments';

  /// 체크리스트 목록 캐시 키
  static String checklistListKey(int workspaceId) => 'workspace_${workspaceId}_checklists';

  /// 목표 수익 목록 캐시 키
  static String revenueGoalListKey(int workspaceId) => 'workspace_${workspaceId}_revenue_goals';
}
