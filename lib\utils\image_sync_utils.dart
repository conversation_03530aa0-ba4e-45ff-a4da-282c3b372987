import 'dart:io';
import '../services/database_service.dart';
import 'logger_utils.dart';

/// 이미지 동기화 공통 유틸리티
///
/// Firebase Storage와의 이미지 업로드/다운로드를 통합 관리하며,
/// 일관된 파일명 규칙과 에러 처리를 제공합니다.
class ImageSyncUtils {
  static const String _tag = 'ImageSyncUtils';

  // 로컬 전용 모드: Firebase 인스턴스 제거됨



  /// 로컬 전용 모드: Firebase 업로드 제거
  /// 상품 이미지는 로컬에만 저장됩니다.
  static Future<String?> uploadProductImageWithName(
    int eventId,
    String categoryName,
    String productName,
    String localImagePath
  ) async {
    return localImagePath; // 로컬 경로 그대로 반환
  }

  /// 상품 객체에서 카테고리 ID를 조회하여 이미지 업로드 (편의 함수)
  ///
  /// [eventId]: 행사 ID
  /// [categoryId]: 카테고리 ID
  /// [productName]: 상품명
  /// [localImagePath]: 로컬 이미지 경로
  ///
  /// Returns: 업로드된 이미지의 다운로드 URL 또는 null
  static Future<String?> uploadProductImageWithCategoryId(
    int eventId,
    int categoryId,
    String productName,
    String localImagePath
  ) async {
    LoggerUtils.logInfo('상품 이미지 업로드 시작: $productName (카테고리ID: $categoryId)', tag: _tag);

    final categoryName = await _getCategoryNameById(categoryId);
    LoggerUtils.logDebug('조회된 카테고리명: $categoryName', tag: _tag);

    return uploadProductImageWithName(eventId, categoryName, productName, localImagePath);
  }

  /// 로컬 전용 모드: Firebase 업로드 제거
  /// 행사 이미지는 로컬에만 저장됩니다.
  static Future<String?> uploadEventImage(
    int eventId,
    String localImagePath
  ) async {
    return localImagePath; // 로컬 경로 그대로 반환
  }

  /// 로컬 전용 모드: Firebase 파일명 변경 제거
  /// 상품 이미지는 로컬에서만 관리됩니다.
  static Future<String?> renameProductImageInStorage(
    int eventId,
    String oldCategoryName,
    String oldProductName,
    String newCategoryName,
    String newProductName,
    String? currentImageUrl
  ) async {
    return currentImageUrl; // 로컬 전용이므로 기존 URL 그대로 반환
  }

  /// 카테고리 이름 변경 시 해당 카테고리의 모든 상품 이미지 파일명 변경 (복사 방식)
  ///
  /// [eventId]: 행사 ID
  /// [oldCategoryName]: 기존 카테고리명
  /// [newCategoryName]: 새 카테고리명
  /// [products]: 해당 카테고리의 상품 리스트 (name, currentImageUrl)
  ///
  /// Returns: 업데이트된 상품별 이미지 URL 맵
  static Future<Map<String, String?>> renameCategoryProductImagesInStorage(
    int eventId,
    String oldCategoryName,
    String newCategoryName,
    List<MapEntry<String, String?>> products
  ) async {
    try {
      LoggerUtils.logInfo('카테고리 이미지 파일명 일괄 변경 시작: $oldCategoryName → $newCategoryName (${products.length}개 상품)', tag: _tag);

      final results = <String, String?>{};

      // 병렬 처리로 성능 향상 (5개씩 배치)
      const batchSize = 5;
      for (int i = 0; i < products.length; i += batchSize) {
        final batch = products.skip(i).take(batchSize).toList();

        final batchResults = await Future.wait(
          batch.map((product) async {
            final productName = product.key;
            final currentImageUrl = product.value;

            try {
              final newImageUrl = await renameProductImageInStorage(
                eventId,
                oldCategoryName,
                productName,
                newCategoryName,
                productName,
                currentImageUrl,
              );
              return MapEntry(productName, newImageUrl);
            } catch (e) {
              LoggerUtils.logWarning('상품 $productName 이미지 파일명 변경 실패', tag: _tag, error: e);
              return MapEntry(productName, currentImageUrl); // 실패 시 기존 URL 유지
            }
          }),
        );

        for (final result in batchResults) {
          results[result.key] = result.value;
        }
      }

      LoggerUtils.logInfo('카테고리 이미지 파일명 일괄 변경 완료: ${results.length}개 처리', tag: _tag);
      return results;
    } catch (e) {
      LoggerUtils.logError('카테고리 이미지 파일명 일괄 변경 실패', tag: _tag, error: e);
      return {};
    }
  }

  /// 로컬 전용 모드: Firebase 다운로드 제거
  /// 상품 이미지는 로컬에서만 관리됩니다.
  static Future<String?> downloadProductImage(
    int eventId,
    int productId,
    String downloadUrl
  ) async {
    return null; // 로컬 전용이므로 다운로드 불필요
  }

  /// 대량 상품 이미지 업로드 (새로운 방식 - 카테고리명_상품명.jpg)
  ///
  /// [eventId]: 행사 ID
  /// [imageDataList]: 업로드할 이미지 데이터 리스트 (categoryName, productName, localImagePath)
  /// [onProgress]: 진행률 콜백
  ///
  /// Returns: 업로드된 이미지 URL 맵 (productName -> downloadUrl)
  static Future<Map<String, String?>> bulkUploadProductImagesWithName(
    int eventId,
    List<MapEntry<String, MapEntry<String, String>>> imageDataList, {
    Function(int current, int total)? onProgress,
  }) async {
    const String logContext = '대량 상품 이미지 업로드 (새로운 방식)';
    LoggerUtils.logInfo('$logContext 시작: ${imageDataList.length}개', tag: _tag);

    final results = <String, String?>{};

    try {
      // 20개씩 배치로 나누어 처리 (Firebase 제한 고려)
      const batchSize = 20;
      for (int i = 0; i < imageDataList.length; i += batchSize) {
        final batch = imageDataList.skip(i).take(batchSize).toList();

        // 배치 내에서 병렬 업로드
        final batchTasks = batch.map((entry) => () async {
          final productName = entry.key;
          final categoryName = entry.value.key;
          final imagePath = entry.value.value;

          try {
            final uploadedUrl = await uploadProductImageWithName(eventId, categoryName, productName, imagePath);
            return MapEntry(productName, uploadedUrl);
          } catch (e) {
            LoggerUtils.logWarning('상품 $productName 이미지 업로드 실패', tag: _tag, error: e);
            return MapEntry(productName, null);
          }
        }).toList();

        // 배치 실행 (15개 동시 업로드)
        final batchResults = await Future.wait(batchTasks.map((task) => task()));

        // 결과 수집
        for (final result in batchResults) {
          results[result.key] = result.value;
        }

        // 진행률 업데이트
        final currentProgress = i + batch.length;
        onProgress?.call(currentProgress, imageDataList.length);

        // 배치 간 잠시 대기 (Firebase 부하 분산)
        if (i + batchSize < imageDataList.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      final successCount = results.values.where((url) => url != null).length;
      LoggerUtils.logInfo('$logContext 완료: $successCount/${imageDataList.length}개 성공', tag: _tag);
      return results;

    } catch (e) {
      LoggerUtils.logError('$logContext 실패', tag: _tag, error: e);
      return {};
    }
  }

  /// 로컬 전용 모드: Firebase 다운로드 제거
  /// 행사 이미지는 로컬에서만 관리됩니다.
  static Future<String?> downloadEventImage(
    int eventId,
    String downloadUrl
  ) async {
    return null; // 로컬 전용이므로 다운로드 불필요
  }



  // 로컬 전용 모드: Firebase 다운로드 관련 메서드들 제거됨

  // 로컬 전용 모드: Firebase 사용자 ID 관련 메서드 제거됨

  // 로컬 전용 모드: Firebase 파일명 정리 메서드 제거됨



  /// 이미지 경로가 로컬 파일인지 확인
  static bool isLocalImagePath(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;

    // HTTP/HTTPS URL이 아니고, 절대 경로이거나 상대 경로인 경우 로컬 파일로 판단
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return false;
    }

    // 파일 경로 패턴 확인 (Windows, Unix 경로 모두 지원)
    return imagePath.contains('/') || imagePath.contains('\\') || imagePath.contains('.');
  }

  /// 이미지 경로가 네트워크 URL인지 확인
  static bool isNetworkImagePath(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  /// 로컬 이미지 파일이 존재하는지 확인
  static Future<bool> localImageExists(String imagePath) async {
    if (!isLocalImagePath(imagePath)) return false;

    try {
      final file = File(imagePath);
      final exists = await file.exists();

      if (exists) {
        // 파일 크기도 확인하여 유효한 이미지인지 검증
        final fileSize = await file.length();
        if (fileSize <= 0) {
          LoggerUtils.logWarning('이미지 파일이 비어있음: $imagePath', tag: 'ImageSyncUtils');
          return false;
        }
      }

      return exists;
    } catch (e) {
      LoggerUtils.logError('이미지 파일 존재 확인 실패: $imagePath', tag: 'ImageSyncUtils', error: e);
      return false;
    }
  }

  /// 이미지 캐시 정리 (특정 행사) - 메모리 캐시만, 파일은 보존
  static Future<void> clearEventImageCache(int eventId) async {
    try {
      // 실제 파일은 삭제하지 않고 메모리 캐시만 정리
      // 상품 이미지와 행사 이미지 파일은 사용자 데이터이므로 보존해야 함

      LoggerUtils.logInfo('행사 $eventId 이미지 메모리 캐시만 정리 (파일은 보존)', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('행사 $eventId 이미지 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 전체 이미지 캐시 정리 (메모리 캐시만, 파일은 보존)
  static Future<void> clearAllImageCache() async {
    try {
      // 실제 파일은 삭제하지 않고 메모리 캐시만 정리
      // 상품 이미지와 행사 이미지 파일은 사용자 데이터이므로 보존해야 함

      LoggerUtils.logInfo('이미지 메모리 캐시만 정리 (파일은 보존)', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('이미지 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 카테고리 ID로 카테고리명 조회
  ///
  /// [categoryId]: 카테고리 ID
  ///
  /// Returns: 카테고리명 또는 '기본 카테고리' (조회 실패시)
  static Future<String> _getCategoryNameById(int categoryId) async {
    try {
      LoggerUtils.logDebug('카테고리 조회 시작: categoryId=$categoryId', tag: _tag);

      // DatabaseService 인스턴스 생성 (직접 접근)
      final databaseService = DatabaseServiceImpl();
      final db = await databaseService.database;

      final result = await db.query(
        DatabaseServiceImpl.categoriesTable,
        columns: ['name'],
        where: 'id = ?',
        whereArgs: [categoryId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final categoryName = result.first['name'] as String;
        LoggerUtils.logDebug('카테고리 조회 성공: $categoryName', tag: _tag);
        return categoryName;
      }

      LoggerUtils.logWarning('카테고리 ID $categoryId를 찾을 수 없음', tag: _tag);
      return '기본카테고리';

    } catch (e) {
      LoggerUtils.logError('카테고리 조회 실패: categoryId=$categoryId', tag: _tag, error: e);
      return '기본카테고리';
    }
  }
}
