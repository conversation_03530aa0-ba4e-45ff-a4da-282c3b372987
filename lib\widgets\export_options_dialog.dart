import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/app_colors.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/sales_log_provider.dart';
import '../providers/prepayment_provider.dart';


/// 데이터 내보내기 옵션 선택 다이얼로그
class ExportOptionsDialog extends ConsumerStatefulWidget {
  final String? selectedSeller;
  final DateTimeRange? selectedDateRange;

  const ExportOptionsDialog({
    super.key,
    this.selectedSeller,
    this.selectedDateRange,
  });

  @override
  ConsumerState<ExportOptionsDialog> createState() => _ExportOptionsDialogState();
}

class _ExportOptionsDialogState extends ConsumerState<ExportOptionsDialog> {
  String _selectedOption = 'full'; // 'current' 또는 'full'

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600;

    return Dialog(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isTablet ? 500 : 350,
          maxHeight: isTablet ? 600 : 500,
        ),
        padding: EdgeInsets.all(isTablet ? 24 : 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목
            _buildHeader(isTablet),

            SizedBox(height: isTablet ? 24 : 20),

            // 옵션 선택
            _buildOptions(isTablet),

            SizedBox(height: isTablet ? 24 : 20),

            // 버튼
            _buildButtons(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(bool isTablet) {
    return Row(
      children: [
        Icon(
          Icons.download,
          color: AppColors.primarySeed,
          size: isTablet ? 28 : 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            '데이터 내보내기 옵션',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOptions(bool isTablet) {
    return Column(
      children: [
        // 현재 화면 설정 옵션
        _buildOptionTile(
          isTablet: isTablet,
          value: 'current',
          title: '현재 화면 설정',
          subtitle: _getCurrentSettingsDescription(),
          icon: Icons.filter_alt,
          dataCount: _getCurrentDataCount(),
        ),
        
        const SizedBox(height: 12),
        
        // 행사 전체 데이터 옵션
        _buildOptionTile(
          isTablet: isTablet,
          value: 'full',
          title: '행사 전체 데이터',
          subtitle: _getFullEventDescription(),
          icon: Icons.event,
          dataCount: _getFullDataCount(),
          isRecommended: true,
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required bool isTablet,
    required String value,
    required String title,
    required String subtitle,
    required IconData icon,
    required int dataCount,
    bool isRecommended = false,
  }) {
    final isSelected = _selectedOption == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedOption = value;
        });
      },
      child: Container(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primarySeed : AppColors.neutral30,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? AppColors.primarySeed.withValues(alpha: 0.05) : null,
        ),
        child: Row(
          children: [
            // 라디오 버튼
            Container(
              width: isTablet ? 24 : 20,
              height: isTablet ? 24 : 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? AppColors.primarySeed : AppColors.neutral50,
                  width: 2,
                ),
                color: isSelected ? AppColors.primarySeed : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: isTablet ? 14 : 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            
            const SizedBox(width: 12),
            
            // 아이콘
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primarySeed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.primarySeed,
                size: isTablet ? 20 : 18,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 텍스트 정보
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface,
                        ),
                      ),
                      if (isRecommended) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '추천',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 10 : 9,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: isTablet ? 13 : 12,
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '예상 데이터: ${dataCount.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}건',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: isTablet ? 12 : 11,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primarySeed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons(bool isTablet) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: isTablet ? 16 : 14),
              side: BorderSide(color: AppColors.neutral30),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              '취소',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(_selectedOption);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: isTablet ? 16 : 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
            ),
            child: Text(
              '내보내기',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getCurrentSettingsDescription() {
    final parts = <String>[];
    
    if (widget.selectedDateRange != null) {
      final start = widget.selectedDateRange!.start;
      final end = widget.selectedDateRange!.end;
      parts.add('${start.month}/${start.day} - ${end.month}/${end.day}');
    }
    
    if (widget.selectedSeller != null && widget.selectedSeller != '전체 판매자') {
      parts.add(widget.selectedSeller!);
    }
    
    if (parts.isEmpty) {
      return '현재 적용된 필터 없음';
    }
    
    return '필터: ${parts.join(', ')}';
  }

  String _getFullEventDescription() {
    final workspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
    if (workspace == null) return '전체 행사 기간';
    
    final start = workspace.startDate;
    final end = workspace.endDate;
    
    return '기간: ${start.month}/${start.day} - ${end.month}/${end.day} (전체 행사)';
  }

  int _getCurrentDataCount() {
    final salesLogState = ref.read(salesLogNotifierProvider);
    final prepaymentState = ref.read(prepaymentNotifierProvider);

    // 현재 필터 적용한 데이터 개수 계산
    final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
    final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);

    return filteredSalesLogs.length + filteredPrepayments.length;
  }

  int _getFullDataCount() {
    final salesLogState = ref.read(salesLogNotifierProvider);
    final prepaymentState = ref.read(prepaymentNotifierProvider);

    return salesLogState.salesLogs.length + prepaymentState.prepayments.length;
  }

  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // 판매자 필터
      if (widget.selectedSeller != null && 
          widget.selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != widget.selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  List<Prepayment> _getFilteredPrepayments(List<Prepayment> allPrepayments) {
    return allPrepayments.where((prepayment) {
      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final prepaymentDate = prepayment.registrationDate;
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (prepaymentDate.isBefore(startDate) || prepaymentDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }
}
