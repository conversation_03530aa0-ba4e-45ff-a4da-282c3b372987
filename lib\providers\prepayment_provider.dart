import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../models/prepayment.dart';
import '../models/prepayment_sort_order.dart';
import '../repositories/prepayment_repository.dart';
import '../utils/logger_utils.dart';
import '../services/database_service.dart';
import '../services/link_service.dart';
// 로컬 전용 모드: 구독 서비스 제거됨
import 'prepayment_state.dart';
import 'settings_provider.dart';
import 'product_provider.dart';
import 'prepayment_virtual_product_provider.dart';
import '../utils/excel_processor.dart';
import 'package:flutter/material.dart';
import 'prepayment_product_link_provider.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/event_workspace.dart';
import 'unified_workspace_provider.dart';
import '../mixins/workspace_aware_provider_mixin.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨
// import 'realtime_sync_provider.dart';
// import '../services/realtime_sync_service_main.dart';

// import 'data_sync_provider.dart'; // 고유 ID 전략으로 단일 업로드 직접 realtime 서비스 사용
import '../utils/prepayment_id_generator.dart';
// 로컬 전용 모드: 사용하지 않는 import 제거됨


/// 선결제 데이터베이스 접근을 위한 Repository Provider입니다.
final prepaymentRepositoryProvider = Provider<PrepaymentRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PrepaymentRepository(database: databaseService);
});

/// 선결제 상태를 관리하는 Provider입니다.
/// - 선결제 목록, CRUD, 상태 변화, 예외 처리 등 모든 비즈니스 로직 담당
class PrepaymentNotifier extends StateNotifier<PrepaymentState> with WorkspaceAwareProviderMixin<PrepaymentState> {
  static const String _pageKey = 'prepayment';

  @override
  final Ref ref;
  final bool autoInit;

  @override
  String get providerTag => 'PrepaymentNotifier';
  bool _isPaused = false;
  // 로컬 전용 모드: 실시간 동기화 제거됨

  // [신규] 임시 선입금 데이터 리스트
  final List<Prepayment> _tempPrepaymentList = [];

  // 무한 루프 방지를 위한 최근 추가한 선입금 캐시
  final Set<int> _recentlyAddedPrepayments = <int>{};

  // 순환 호출 방지를 위한 플래그
  bool _isNotifyingRelatedProviders = false;

  // 가상 상품 이름 변경 중 플래그 (가상 상품 자동 생성 방지용)
  bool _isUpdatingVirtualProductName = false;

  PrepaymentNotifier(this.ref, {this.autoInit = true}) : super(PrepaymentState.initialState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    await _restoreSettings();
    if (autoInit) {
      await loadPrepayments();
    }
    watchCurrentEvent(); // Mixin의 메서드 사용
    _setupRealtimeSync();
  }

  /// Mixin에서 요구하는 워크스페이스 변경 처리 메서드
  @override
  Future<void> onWorkspaceChanged(EventWorkspace? workspace) async {
    if (workspace != null) {
      await loadPrepayments();
      _setupRealtimeSync(); // 실시간 동기화 활성화
    } else {
      // 현재 행사 워크스페이스가 null이 되면 선입금 목록 클리어
      state = state.copyWith(
        prepayments: [],
        filteredPrepayments: [],
        errorMessage: getNoWorkspaceErrorMessage(),
      );
    }
  }

  /// Mixin에서 요구하는 메모리 정리 메서드
  @override
  void clearDataForEventTransition() {
    _clearAllDataForEventTransition();
  }

  /// Mixin에서 요구하는 실시간 동기화 설정 메서드
  @override
  void setupRealtimeSync() {
    _setupRealtimeSync();
  }

  /// 로컬 전용 모드: 실시간 동기화 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드에서는 실시간 동기화를 사용하지 않음
  }

  /// 행사 전환 시 메모리 완전 클리어 (메모리 누수 방지)
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 시작', tag: 'PrepaymentNotifier');

      // 1. 상태 완전 초기화
      state = PrepaymentState.initialState();

      // 2. 내부 변수 클리어
      _tempPrepaymentList.clear();

      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 완료', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('메모리 클리어 중 오류', tag: 'PrepaymentNotifier', error: e);
    }
  }

  /// 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  /// PrepaymentNotifier dispose 처리
  @override
  void dispose() {
    super.dispose();
  }

  /// 레거시 id==0 선입금들을 새로운 고유 ID로 재할당 (최초 한 번 실행)
  Future<void> _migrateZeroIdPrepayments(List<Prepayment> zeroIdList) async {
    if (zeroIdList.isEmpty) return;
    LoggerUtils.logInfo('레거시 ID=0 선입금 마이그레이션 시작: ${zeroIdList.length}개', tag: 'PrepaymentNotifier');
    final repository = ref.read(prepaymentRepositoryProvider);
    // 로컬 전용 모드: 실시간 동기화 서비스 제거됨
    // 로컬 전용 모드: 사용하지 않는 변수 제거됨
    for (final old in zeroIdList) {
      try {
        final newId = PrepaymentIdGenerator.generate();
        final migrated = old.copyWith(id: newId);
        // 새 레코드 삽입
        await repository.insertPrepayment(migrated);
        // 오래된 0 ID 레코드 삭제
        await repository.deletePrepayment(old.id);
        // 로컬 전용 모드: 원격 동기화 제거됨
      } catch (e) {
        LoggerUtils.logError('ID=0 마이그레이션 실패', tag: 'PrepaymentNotifier', error: e);
      }
    }
    await loadPrepayments(showLoading: false);
    LoggerUtils.logInfo('레거시 ID=0 선입금 마이그레이션 완료', tag: 'PrepaymentNotifier');
  }

  /// [신규] 임시 선입금 데이터 추가
  Future<void> addTempPrepaymentData(Prepayment prepayment) async {
    _tempPrepaymentList.add(prepayment);
  }

  /// [신규] 임시 선입금 데이터 전체 삭제
  Future<void> clearTempPrepaymentData() async {
    _tempPrepaymentList.clear();
  }

  /// [신규] 임시 선입금 데이터 전체 조회
  List<Prepayment> getTempPrepaymentList() {
    return List.unmodifiable(_tempPrepaymentList);
  }

  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      LoggerUtils.logDebug('PrepaymentNotifier paused', tag: 'PrepaymentNotifier');
    }
  }

  void resume([Duration? duration]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('PrepaymentNotifier resumed', tag: 'PrepaymentNotifier');
      loadPrepayments(showLoading: false);
    }
  }

  Future<void> loadPrepayments({bool showLoading = true}) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('loadPrepayments', tag: 'PrepaymentNotifier');

    // 선입금 관리는 모든 플랜에서 로컬 사용 가능 (서버 동기화만 프로 플랜 제한)

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    int retry = 0;
    while (retry < 3) {
      try {
        // 현재 선택된 행사 워크스페이스 확인
        EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

        if (currentWorkspace == null) {
          LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
          state = state.copyWith(
            prepayments: [],
            filteredPrepayments: [],
            isLoading: false,
            errorMessage: '워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
          return;
        }

        final repository = ref.read(prepaymentRepositoryProvider);
        final prepayments = await repository.getPrepaymentsByEventId(currentWorkspace.id);

        // 디버깅 로그 제거됨 (성능 최적화)

        final filteredPrepayments = _applyFiltersAndSort(prepayments, state);
        // 필터링 결과 로그 제거됨 (성능 최적화)

        state = state.copyWith(
          prepayments: prepayments,
          filteredPrepayments: filteredPrepayments,
          isLoading: false,
          errorMessage: null,
        );
        // ID 0 레거시 데이터 마이그레이션
        if (prepayments.any((p) => p.id == 0)) {
          _migrateZeroIdPrepayments(prepayments.where((p) => p.id == 0).toList());
        }
        return;
      } catch (e) {
        final msg = e.toString();
        if (msg.contains('no such table') || msg.contains('database is not open')) {
          retry++;
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        } else {
          state = state.copyWith(
            isLoading: false,
            errorMessage: msg,
          );
          return;
        }
      }
    }
    state = state.copyWith(isLoading: false, errorMessage: '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.');
  }

  /// 다음 선입금 번호 생성
  Future<int> _getNextPrepaymentNumber(int eventId) async {
    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.getPrepaymentsByEventId(eventId);

      // 현재 행사의 모든 선입금에서 가장 큰 선입금 번호 찾기
      int maxNumber = 0;
      for (final prepayment in prepayments) {
        if (prepayment.prepaymentNumber != null && prepayment.prepaymentNumber! > maxNumber) {
          maxNumber = prepayment.prepaymentNumber!;
        }
      }

      return maxNumber + 1;
    } catch (e) {
      LoggerUtils.logError('선입금 번호 생성 실패', tag: 'PrepaymentNotifier', error: e);
      // 에러 발생 시 현재 시간 기반으로 번호 생성
      return DateTime.now().millisecondsSinceEpoch % 100000;
    }
  }

  Future<void> addPrepayment(Prepayment prepayment) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('addPrepayment', tag: 'PrepaymentNotifier');

    try {
      // 현재 선택된 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
        state = state.copyWith(errorMessage: '워크스페이스를 선택해주세요');
        return;
      }

      // 고유 ID 미할당(id==0) 시 즉시 생성 (DB/Firestore 모두 동일 사용)
      if (prepayment.id == 0) {
        try {
          final newId = PrepaymentIdGenerator.generate();
          prepayment = prepayment.copyWith(id: newId);
        } catch (e) {
          LoggerUtils.logError('ID 생성 실패, 타임스탬프 fallback', tag: 'PrepaymentNotifier', error: e);
          prepayment = prepayment.copyWith(id: DateTime.now().microsecondsSinceEpoch);
        }
      }

      // 선입금 번호 생성 (선입금 번호가 없는 경우에만)
      int? prepaymentNumber = prepayment.prepaymentNumber;
      if (prepaymentNumber == null) {
        prepaymentNumber = await _getNextPrepaymentNumber(currentWorkspace.id);
      }

      // 선입금에 현재 워크스페이스 ID와 선입금 번호 설정 (id는 유지)
      final prepaymentWithEventId = prepayment.copyWith(
        eventId: currentWorkspace.id,
        prepaymentNumber: prepaymentNumber,
      );

      // 1. 로컬 DB에 저장
      final repository = ref.read(prepaymentRepositoryProvider);
      LoggerUtils.logDebug('PrepaymentNotifier - insertPrepayment 호출 시작', tag: 'PrepaymentNotifier');
      final insertResult = await repository.insertPrepayment(prepaymentWithEventId);
      LoggerUtils.logInfo('PrepaymentNotifier - insertPrepayment 결과: $insertResult', tag: 'PrepaymentNotifier');
      // insert가 -1 (오프라인) 이거나 0(무시) 이어도 우리는 이미 고유 ID를 가지고 있다.
      final savedPrepayment = prepaymentWithEventId;

      // 최근 추가한 선입금으로 캐시 (무한 루프 방지용)
      _recentlyAddedPrepayments.add(savedPrepayment.id);
      // 5초 후 캐시에서 제거
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedPrepayments.remove(savedPrepayment.id);
      });

      // 로컬 전용 모드: Firebase 업로드 제거됨

      // 상태 업데이트 복원 - 선입금 등록 후 즉시 목록 갱신
      await loadPrepayments(showLoading: false);

      // 신규 선입금의 상품을 가상 상품 수량에 반영
      await _addVirtualProductQuantitiesForNewPrepayment(savedPrepayment);

      // 관련 Provider들에게 선입금 추가 알림
      _notifyRelatedProviders(savedPrepayment);

    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('addPrepayment', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> updatePrepayment(Prepayment prepayment) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('updatePrepayment', tag: 'PrepaymentNotifier');

    try {
      // 수정 전에 기존 선입금 정보를 가져와서 가상 상품 수량 차감에 사용
      final existingPrepayment = state.prepayments.firstWhere(
        (p) => p.id == prepayment.id,
        orElse: () => throw Exception('수정할 선입금을 찾을 수 없습니다.'),
      );

      final repository = ref.read(prepaymentRepositoryProvider);
      await repository.updatePrepayment(prepayment);
      await loadPrepayments(showLoading: false);
      
      // 기존 상품 수량을 차감하고 새로운 상품 수량을 추가
      await _updateVirtualProductQuantities(existingPrepayment, prepayment);

      // 관련 Provider들에게 선입금 업데이트 알림
      _notifyRelatedProviders(prepayment);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('updatePrepayment', tag: 'PrepaymentNotifier');
    }
  }

  /// 선입금의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updatePrepaymentFields(int prepaymentId, Map<String, dynamic> fields) async {
    if (_isPaused) return;

    try {
      LoggerUtils.methodStart('updatePrepaymentFields', tag: 'PrepaymentNotifier', data: {'id': prepaymentId, 'fields': fields.keys.toList()});

      // 현재 선입금 조회
      final currentPrepayment = state.prepayments.firstWhere(
        (p) => p.id == prepaymentId,
        orElse: () => throw Exception('선입금을 찾을 수 없습니다: $prepaymentId'),
      );

      // 필드 업데이트를 위한 copyWith 호출
      Prepayment updatedPrepayment = currentPrepayment;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('buyerName')) {
        updatedPrepayment = updatedPrepayment.copyWith(buyerName: fields['buyerName'] as String);
      }

      if (fields.containsKey('buyerContact')) {
        updatedPrepayment = updatedPrepayment.copyWith(buyerContact: fields['buyerContact'] as String);
      }

      if (fields.containsKey('amount')) {
        updatedPrepayment = updatedPrepayment.copyWith(amount: fields['amount'] as int);
      }

      if (fields.containsKey('pickupDays')) {
        updatedPrepayment = updatedPrepayment.copyWith(pickupDays: fields['pickupDays'] as List<String>);
      }

      if (fields.containsKey('memo')) {
        final memo = fields['memo'] as String?;
        updatedPrepayment = updatedPrepayment.copyWith(memo: memo);
      }

      if (fields.containsKey('isReceived')) {
        updatedPrepayment = updatedPrepayment.copyWith(isReceived: fields['isReceived'] as bool);
      }

      if (fields.containsKey('bankName')) {
        updatedPrepayment = updatedPrepayment.copyWith(bankName: fields['bankName'] as String);
      }

      if (fields.containsKey('email')) {
        updatedPrepayment = updatedPrepayment.copyWith(email: fields['email'] as String);
      }

      if (fields.containsKey('twitterAccount')) {
        final twitterAccount = fields['twitterAccount'] as String?;
        updatedPrepayment = updatedPrepayment.copyWith(twitterAccount: twitterAccount);
      }

      if (fields.containsKey('orderNumber')) {
        final orderNumber = fields['orderNumber'] as String?;
        updatedPrepayment = updatedPrepayment.copyWith(orderNumber: orderNumber);
      }

      // 로컬 DB 업데이트
      final repository = ref.read(prepaymentRepositoryProvider);
      await repository.updatePrepayment(updatedPrepayment);

      // 로컬 전용 모드: 실시간 동기화 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 선입금 필드 업데이트 서버 업로드 건너뜀: ${fields.keys.join(', ')}', tag: 'PrepaymentNotifier');

      // 관련 Provider들에게 선입금 업데이트 알림
      _notifyRelatedProviders(updatedPrepayment);

      // 상태 갱신
      await loadPrepayments(showLoading: false);

      LoggerUtils.methodEnd('updatePrepaymentFields', tag: 'PrepaymentNotifier');
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '선입금 필드 업데이트 실패',
        tag: 'PrepaymentNotifier',
        error: e,
        stackTrace: stackTrace,
        data: {'id': prepaymentId, 'fields': fields.keys.toList()},
      );

      state = state.copyWith(
        errorMessage: '선입금 필드 업데이트에 실패했습니다: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// 관련 Provider들에게 선입금 업데이트 알림 (순환 호출 방지)
  void _notifyRelatedProviders(Prepayment updatedPrepayment) {
    // 순환 호출 방지: 이미 알림 중이면 건너뛰기
    if (_isNotifyingRelatedProviders) {
      LoggerUtils.logDebug('순환 호출 방지: 선입금 관련 Provider 알림 건너뛰기 - ${updatedPrepayment.buyerName}', tag: 'PrepaymentNotifier');
      return;
    }

    try {
      _isNotifyingRelatedProviders = true;

      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 상품 Provider 갱신 (선입금과 연관된 상품이 있는 경우)
          ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

          LoggerUtils.logInfo('선입금 관련 Provider들 갱신 완료: ${updatedPrepayment.buyerName}', tag: 'PrepaymentNotifier');
        } catch (e) {
          LoggerUtils.logError('선입금 관련 Provider 갱신 실패', tag: 'PrepaymentNotifier', error: e);
        } finally {
          // 플래그 해제 (다음 호출을 위해)
          _isNotifyingRelatedProviders = false;
        }
      });
    } catch (e) {
      _isNotifyingRelatedProviders = false; // 에러 시에도 플래그 해제
      LoggerUtils.logError('선입금 관련 Provider 알림 실패', tag: 'PrepaymentNotifier', error: e);
    }
  }

  Future<void> deletePrepayment(int id) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('deletePrepayment', tag: 'PrepaymentNotifier');

    try {
      // 삭제 전에 선입금 정보를 가져와서 가상 상품 수량 차감에 사용
      final prepaymentToDelete = state.prepayments.firstWhere(
        (p) => p.id == id,
        orElse: () => throw Exception('삭제할 선입금을 찾을 수 없습니다.'),
      );

      final repository = ref.read(prepaymentRepositoryProvider);
      await repository.deletePrepayment(id);
      await loadPrepayments(showLoading: false);

      // 삭제된 선입금의 상품 수량을 가상 상품 데이터에서 차감
      await _subtractVirtualProductQuantities(prepaymentToDelete);

      // 관련 Provider들에게 선입금 삭제 알림
      _notifyRelatedProviders(prepaymentToDelete);

      // 로컬 전용 모드: Firebase 삭제 제거됨
      LoggerUtils.logInfo('로컬 전용 모드: 선입금 Firebase 삭제 건너뜀: ID $id', tag: 'PrepaymentNotifier');
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('deletePrepayment', tag: 'PrepaymentNotifier');
    }
  }

  /// 선입금 배치 삭제
  Future<void> batchDeletePrepayments(List<int> ids) async {
    if (_isPaused || ids.isEmpty) return;
    LoggerUtils.methodStart('batchDeletePrepayments', tag: 'PrepaymentNotifier');

    try {
      // 삭제 전에 선입금 정보들을 가져와서 가상 상품 수량 차감에 사용
      final prepaymentsToDelete = state.prepayments.where((p) => ids.contains(p.id)).toList();

      if (prepaymentsToDelete.isEmpty) {
        LoggerUtils.logWarning('삭제할 선입금을 찾을 수 없습니다: $ids', tag: 'PrepaymentNotifier');
        return;
      }

      final repository = ref.read(prepaymentRepositoryProvider);

      // 배치 삭제 실행
      await repository.batchDeletePrepayments(ids);
      await loadPrepayments(showLoading: false);

      // 삭제된 선입금들의 상품 수량을 가상 상품 데이터에서 차감
      for (final prepayment in prepaymentsToDelete) {
        await _subtractVirtualProductQuantities(prepayment);
      }

      // 관련 Provider들에게 선입금 삭제 알림
      for (final prepayment in prepaymentsToDelete) {
        _notifyRelatedProviders(prepayment);
      }

      // 로컬 전용 모드: Firebase 배치 삭제 제거됨

      LoggerUtils.logInfo('선입금 배치 삭제 완료: ${ids.length}개', tag: 'PrepaymentNotifier');
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('batchDeletePrepayments', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> updateReceiveStatus(BuildContext context, int id, bool isReceived) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('updateReceiveStatus', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final databaseService = ref.read(databaseServiceProvider);
      final linkService = LinkService(databaseService: databaseService);
      // 수령 상태 변경 전에 현재 선입금 정보 가져오기
      final currentPrepayment = await repository.getPrepaymentById(id);
      if (currentPrepayment == null) {
        LoggerUtils.logError('선입금을 찾을 수 없음: ID $id', tag: 'PrepaymentNotifier');
        return;
      }
      await repository.updateReceiveStatus(id, isReceived);
      final linkPrepaymentToInventory = ref.read(linkPrepaymentToInventoryProvider);
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) return;

      if (linkPrepaymentToInventory) {
        if (isReceived) {
          // 수령 완료로 변경된 경우: 연동 및 재고 차감 (LinkService만 호출)
          await linkService.linkProductsWithPrepayments(
            prepaymentDataList: [ExcelProcessor.fromPrepayment(currentPrepayment)],
            context: context,
            eventId: currentWorkspace.id,
          );
          // 연동된 상품의 재고 차감 - decreaseStock 메서드 사용으로 중복 Firebase 업로드 방지
          final linkRepo = ref.read(prepaymentProductLinkRepositoryProvider);
          final productRepo = ref.read(productRepositoryProvider);
          final virtualProductState = ref.read(prepaymentVirtualProductNotifierProvider);

          for (final purchased in currentPrepayment.purchasedProducts) {
            final vp = virtualProductState.virtualProducts.firstWhere(
              (v) => v.name == purchased.name,
              orElse: () => PrepaymentVirtualProduct(id: -1, name: '', price: 0, quantity: 0, createdAt: DateTime.now(), eventId: 1),
            );
            if (vp.id == -1) continue;
            final links = await linkRepo.getLinksByVirtualProductId(vp.id, currentWorkspace.id);
            for (final link in links) {
              // 재고 차감은 decreaseStock 메서드 사용
              await productRepo.decreaseStock(link.productId, purchased.quantity);
            }
          }
        } else {
          // 미수령으로 되돌린 경우: 연동 해제 없이 재고만 복구
          final linkRepo = ref.read(prepaymentProductLinkRepositoryProvider);
          final productRepo = ref.read(productRepositoryProvider);
          final virtualProductState = ref.read(prepaymentVirtualProductNotifierProvider);
          final currentWorkspace2 = ref.read(currentWorkspaceProvider);
          if (currentWorkspace2 == null) return;

          for (final purchased in currentPrepayment.purchasedProducts) {
            final vp = virtualProductState.virtualProducts.firstWhere(
              (v) => v.name == purchased.name,
              orElse: () => PrepaymentVirtualProduct(id: -1, name: '', price: 0, quantity: 0, createdAt: DateTime.now(), eventId: 1),
            );
            if (vp.id == -1) continue;
            final links = await linkRepo.getLinksByVirtualProductId(vp.id, currentWorkspace2.id);
            for (final link in links) {
              // 재고 복구는 increaseStock 메서드 사용
              await productRepo.increaseStock(link.productId, purchased.quantity);
            }
          }
        }
      }
      await loadPrepayments(showLoading: false);

      // 관련 Provider들에게 수령 상태 변경 알림
      final updatedPrepayment = await repository.getPrepaymentById(id);
      if (updatedPrepayment != null) {
        _notifyRelatedProviders(updatedPrepayment);
      }
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('updateReceiveStatus', tag: 'PrepaymentNotifier');
    }
  }

  /// 가상 상품 데이터를 갱신하는 메서드
  // (이전) _refreshVirtualProducts 메서드는 신규 등록 시 직접 addVirtualProduct 로직으로 대체되어 제거됨

  /// 삭제된 선입금의 상품 수량을 가상 상품 데이터에서 차감하는 메서드
  Future<void> _subtractVirtualProductQuantities(Prepayment deletedPrepayment) async {
    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);
      
      // 삭제된 선입금의 각 상품 수량을 차감
      for (final product in deletedPrepayment.purchasedProducts) {
        await virtualProductNotifier.subtractVirtualProductQuantity(product.name, product.quantity);
      }
    } catch (e) {
      LoggerUtils.logError(
        '가상 상품 데이터 수량 차감 중 오류 발생: $e',
        tag: 'PrepaymentNotifier',
      );
    }
  }

  /// 선입금 수정 시 가상 상품 데이터 수량을 업데이트하는 메서드
  Future<void> _updateVirtualProductQuantities(Prepayment oldPrepayment, Prepayment newPrepayment) async {
    // 가상 상품 이름 변경 중에는 가상 상품 자동 생성을 건너뜀
    if (_isUpdatingVirtualProductName) {
      LoggerUtils.logDebug('가상 상품 이름 변경 중이므로 가상 상품 수량 업데이트 건너뜀', tag: 'PrepaymentNotifier');
      return;
    }

    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);

      // 기존 상품 수량을 차감
      for (final product in oldPrepayment.purchasedProducts) {
        await virtualProductNotifier.subtractVirtualProductQuantity(product.name, product.quantity);
      }

      // 새로운 상품 수량을 추가
      for (final product in newPrepayment.purchasedProducts) {
        virtualProductNotifier.addVirtualProduct(product.name, product.quantity);
      }
    } catch (e) {
      LoggerUtils.logError(
        '가상 상품 데이터 수량 업데이트 중 오류 발생: $e',
        tag: 'PrepaymentNotifier',
      );
    }
  }

  /// 신규 선입금 등록 시 가상 상품 수량을 추가하는 메서드
  Future<void> _addVirtualProductQuantitiesForNewPrepayment(Prepayment newPrepayment) async {
    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);
      if (newPrepayment.purchasedProducts.isEmpty) {
        LoggerUtils.logDebug('신규 선입금에 연결된 상품이 없어 가상 상품 추가 스킵: ID ${newPrepayment.id}', tag: 'PrepaymentNotifier');
        return;
      }
      for (final product in newPrepayment.purchasedProducts) {
        if (product.name.trim().isEmpty || product.quantity <= 0) continue;
        virtualProductNotifier.addVirtualProduct(product.name.trim(), product.quantity);
      }
      LoggerUtils.logInfo('신규 선입금(ID: ${newPrepayment.id}) 가상 상품 수량 반영 완료 (${newPrepayment.purchasedProducts.length}개)', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('신규 선입금 가상 상품 수량 반영 중 오류: $e', tag: 'PrepaymentNotifier');
    }
  }

  /// ID로 특정 선결제 정보를 조회합니다.
  Future<Prepayment?> getPrepaymentById(int id) async {
    final repository = ref.read(prepaymentRepositoryProvider);
    return await repository.getPrepaymentById(id);
  }

  /// 수령 상태를 토글합니다.
  Future<void> toggleReceiveStatus(int id) async {
    final repository = ref.read(prepaymentRepositoryProvider);
    final prepayment = await repository.getPrepaymentById(id);
    if (prepayment != null) {
      await repository.updateReceiveStatus(id, !prepayment.isReceived);
      await loadPrepayments(showLoading: false);
    }
  }

  Future<void> setSortOrder(PrepaymentSortOrder sortOrder) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('setSortOrder', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.getPrepaymentsSorted(sortOrder);

      // 새로운 sortOrder로 상태를 먼저 업데이트
      final newState = state.copyWith(
        prepayments: prepayments,
        sortOrder: sortOrder,
        errorMessage: null,
      );

      // 업데이트된 상태로 필터링 적용
      final filteredPrepayments = _applyFiltersAndSort(prepayments, newState);

      state = newState.copyWith(filteredPrepayments: filteredPrepayments);
      _saveSettings();
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('setSortOrder', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> setReceivedFilter(bool showReceived) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('setReceivedFilter', tag: 'PrepaymentNotifier');

    try {
      state = state.copyWith(showReceivedOnly: showReceived);
      state = state.copyWith(
        filteredPrepayments: _applyFiltersAndSort(state.prepayments, state),
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('setReceivedFilter', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> search(String query) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('search', tag: 'PrepaymentNotifier');

    try {
      state = state.copyWith(searchQuery: query);
      state = state.copyWith(
        filteredPrepayments: _applyFiltersAndSort(state.prepayments, state),
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('search', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> searchPrepayments(String query) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('searchPrepayments', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final searchResults = await repository.searchPrepayments(query);

      // 검색 결과를 filteredPrepayments에 설정하고,
      // 빈 검색어인 경우 전체 데이터를 다시 로드하여 동기화 보장
      if (query.trim().isEmpty) {
        await loadPrepayments(showLoading: false);
      } else {
        state = state.copyWith(
          filteredPrepayments: searchResults,
          searchQuery: query,
          errorMessage: null,
        );
      }
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('searchPrepayments', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> filterByDayOfWeek(int dayOfWeek) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('filterByDayOfWeek', tag: 'PrepaymentNotifier');

    try {
      final allPrepayments = state.prepayments;
      List<Prepayment> filtered;
      if (dayOfWeek == 0) {
        filtered = allPrepayments;
      } else {
        filtered = allPrepayments.where((p) {
          // registrationActualDayOfWeek 체크
          bool match = false;
          if (dayOfWeek == 8) {
            // '없음' 처리
            if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
              match = true;
            }
            if (p.pickupDays.any((d) => d == Prepayment.noDayOfWeek || Prepayment.availableDaysOfWeek.indexOf(d) == 7)) {
              match = true;
            }
          } else {
            if (p.registrationActualDayOfWeek == dayOfWeek) {
              match = true;
            }
            if (p.pickupDays.any((d) => Prepayment.availableDaysOfWeek.indexOf(d) == (dayOfWeek - 1))) {
              match = true;
            }
          }
          return match;
        }).toList();
      }
      state = state.copyWith(filteredPrepayments: filtered, errorMessage: null);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('filterByDayOfWeek', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> loadPrepaymentsSorted(PrepaymentSortOrder sortOrder) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('loadPrepaymentsSorted', tag: 'PrepaymentNotifier');

    try {
      // 현재 선택된 행사 워크스페이스 확인 (최대 3초 대기)
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
        state = state.copyWith(
          prepayments: [],
          filteredPrepayments: [],
          sortOrder: sortOrder,
          errorMessage: '워크스페이스를 선택해주세요',
        );
        return;
      }

      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.getPrepaymentsSorted(sortOrder, eventId: currentWorkspace.id);
      state = state.copyWith(
        prepayments: prepayments,
        filteredPrepayments: prepayments,
        sortOrder: sortOrder,
        errorMessage: null,
      );
      _saveSettings();
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('loadPrepaymentsSorted', tag: 'PrepaymentNotifier');
    }
  }

  /// 필터와 정렬을 적용하여 필터링된 목록을 반환합니다.
  List<Prepayment> _applyFiltersAndSort(List<Prepayment> prepayments, PrepaymentState currentState) {
    List<Prepayment> filtered = List.from(prepayments);

    // 검색 필터 적용
    if (currentState.searchQuery.isNotEmpty) {
      final query = currentState.searchQuery.toLowerCase();
      filtered = filtered.where((prepayment) {
        return prepayment.buyerName.toLowerCase().contains(query) ||
               prepayment.amount.toString().contains(query);
      }).toList();
    }

    // 수령 상태 필터 적용 (기존 showReceivedOnly 유지하면서 새로운 filter도 지원)
    if (currentState.showReceivedOnly) {
      filtered = filtered.where((prepayment) => prepayment.isReceived).toList();
    } else {
      // 새로운 필터 시스템 적용
      switch (currentState.filter) {
        case PrepaymentFilter.received:
          filtered = filtered.where((prepayment) => prepayment.isReceived).toList();
          break;
        case PrepaymentFilter.unreceived:
          filtered = filtered.where((prepayment) => !prepayment.isReceived).toList();
          break;
        case PrepaymentFilter.all:
          // 모든 항목 표시 (필터링 없음)
          break;
      }
    }

    // 정렬 적용
    switch (currentState.sortOrder) {
      case PrepaymentSortOrder.registrationDateDesc:
        filtered.sort((a, b) => b.registrationTimestamp.compareTo(a.registrationTimestamp));
        break;
      case PrepaymentSortOrder.registrationDateAsc:
        filtered.sort((a, b) => a.registrationTimestamp.compareTo(b.registrationTimestamp));
        break;
      case PrepaymentSortOrder.prepaymentNumberAsc:
        filtered.sort((a, b) {
          final aNumber = a.prepaymentNumber ?? 0;
          final bNumber = b.prepaymentNumber ?? 0;
          return aNumber.compareTo(bNumber);
        });
        break;
      case PrepaymentSortOrder.prepaymentNumberDesc:
        filtered.sort((a, b) {
          final aNumber = a.prepaymentNumber ?? 0;
          final bNumber = b.prepaymentNumber ?? 0;
          return bNumber.compareTo(aNumber);
        });
        break;
      case PrepaymentSortOrder.buyerNameAsc:
        filtered.sort((a, b) => a.buyerName.compareTo(b.buyerName));
        break;
      case PrepaymentSortOrder.buyerNameDesc:
        filtered.sort((a, b) => b.buyerName.compareTo(a.buyerName));
        break;
      case PrepaymentSortOrder.amountAsc:
        filtered.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case PrepaymentSortOrder.amountDesc:
        filtered.sort((a, b) => b.amount.compareTo(a.amount));
        break;
    }

    return filtered;
  }

  /// 필터를 설정합니다.
  void setFilter(PrepaymentFilter filter) {
    if (_isPaused) return;

    final currentState = state;
    final filteredPrepayments = _applyFiltersAndSort(currentState.prepayments, currentState.copyWith(filter: filter));

    state = state.copyWith(
      filter: filter,
      filteredPrepayments: filteredPrepayments,
    );
  }

  /// 에러 상태를 초기화합니다.
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 로딩 상태를 설정합니다.
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// 가상 상품 이름 변경 중 플래그를 설정합니다.
  void setVirtualProductNameUpdating(bool isUpdating) {
    _isUpdatingVirtualProductName = isUpdating;
  }

  /// 설정 저장
  Future<void> _saveSettings() async {
    try {
      LoggerUtils.logDebug('선입금 설정 저장 시작: 정렬=${state.sortOrder.name}', tag: 'PrepaymentNotifier');
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 정렬 설정 저장
      await settingsRepository.setPageSortOption(_pageKey, state.sortOrder.name);

      LoggerUtils.logDebug('선입금 설정 저장 완료: 정렬=${state.sortOrder.name}', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('선입금 설정 저장 실패', tag: 'PrepaymentNotifier', error: e);
    }
  }

  /// 설정 복원
  Future<void> _restoreSettings() async {
    try {
      LoggerUtils.logDebug('선입금 설정 복원 시작', tag: 'PrepaymentNotifier');
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 정렬 설정 복원
      final sortOption = await settingsRepository.getPageSortOption(_pageKey);
      LoggerUtils.logDebug('저장된 정렬 옵션: $sortOption', tag: 'PrepaymentNotifier');

      if (sortOption != null) {
        final sortOrder = PrepaymentSortOrder.values.where((s) => s.name == sortOption).firstOrNull ?? PrepaymentSortOrder.prepaymentNumberAsc;

        state = state.copyWith(sortOrder: sortOrder);

        LoggerUtils.logDebug('선입금 설정 복원 완료: 정렬=$sortOrder', tag: 'PrepaymentNotifier');
      } else {
        LoggerUtils.logDebug('저장된 설정이 없어 기본값 사용: ${PrepaymentSortOrder.prepaymentNumberAsc}', tag: 'PrepaymentNotifier');
        state = state.copyWith(sortOrder: PrepaymentSortOrder.prepaymentNumberAsc);
      }
    } catch (e) {
      LoggerUtils.logError('선입금 설정 복원 실패', tag: 'PrepaymentNotifier', error: e);
    }
  }
}

/// 선결제 상태를 관리하는 Provider입니다.
final prepaymentNotifierProvider = StateNotifierProvider<PrepaymentNotifier, PrepaymentState>((ref) {
  return PrepaymentNotifier(ref, autoInit: false); // 자동 초기화 비활성화
});

/// 선결제 요일 필터를 관리하는 Provider입니다.
final prepaymentDayOfWeekFilterProvider = StateProvider<int>((ref) {
  return 0; // 0: 전체, 1-7: 요일, 8: 없음
});

/// 선결제 에러 메시지 Provider
final prepaymentErrorMessageProvider = StateProvider<String?>((ref) {
  final state = ref.watch(prepaymentNotifierProvider);
  return state.errorMessage;
});

/// 선결제 검색어 Provider
final prepaymentSearchQueryProvider = StateProvider<String>((ref) {
  final state = ref.watch(prepaymentNotifierProvider);
  return state.searchQuery;
});

/// Prepayment 데이터 동기화 관리자
class PrepaymentDataSyncManager {
  static const String _tag = 'PrepaymentDataSyncManager';
  
  /// 모든 Prepayment 관련 Provider를 일관되게 갱신
  static Future<void> syncAllPrepaymentData(WidgetRef ref) async {
    LoggerUtils.logInfo('Prepayment 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 갱신
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      
      LoggerUtils.logInfo('Prepayment 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 추가 후 동기화
  static Future<void> syncAfterAddPrepayment(WidgetRef ref, Prepayment prepayment) async {
    LoggerUtils.logInfo('Prepayment 추가 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 추가
      await ref.read(prepaymentNotifierProvider.notifier).addPrepayment(prepayment);
      
      LoggerUtils.logInfo('Prepayment 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 수정 후 동기화
  static Future<void> syncAfterUpdatePrepayment(WidgetRef ref, Prepayment prepayment) async {
    LoggerUtils.logInfo('Prepayment 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 수정
      await ref.read(prepaymentNotifierProvider.notifier).updatePrepayment(prepayment);
      
      LoggerUtils.logInfo('Prepayment 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 삭제 후 동기화
  static Future<void> syncAfterDeletePrepayment(WidgetRef ref, int id) async {
    LoggerUtils.logInfo('Prepayment 삭제 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 삭제
      await ref.read(prepaymentNotifierProvider.notifier).deletePrepayment(id);
      
      LoggerUtils.logInfo('Prepayment 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

/// 실제 등록된 선입금 데이터의 요일만 집계하여 제공하는 Provider
final prepaymentAvailableDaysOfWeekProvider = Provider<Set<int>>((ref) {
  final prepayments = ref.watch(prepaymentNotifierProvider).prepayments;
  final Set<int> days = {};
  for (final p in prepayments) {
    // registrationActualDayOfWeek가 1~7, 8('없음')이면 추가
    if (p.registrationActualDayOfWeek >= 1 && p.registrationActualDayOfWeek <= 7) {
      days.add(p.registrationActualDayOfWeek);
    } else if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
      days.add(8); // '없음'
    }
    // pickupDays에도 요일명이 있으면 추가
    for (final dayName in p.pickupDays) {
      final idx = Prepayment.availableDaysOfWeek.indexOf(dayName);
      if (idx >= 0 && idx < 7) {
        days.add(idx + 1); // 1~7
      } else if (dayName == Prepayment.noDayOfWeek || idx == 7) {
        days.add(8); // '없음'
      }
    }
  }
  return days;
});
