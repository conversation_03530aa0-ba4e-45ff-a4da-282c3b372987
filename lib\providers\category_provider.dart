import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category.dart';
import '../models/event_workspace.dart';
import '../repositories/category_repository.dart';

// 로컬 전용 모드: 실시간 동기화 서비스 제거됨
// 로컬 전용 모드: 실시간 동기화 Provider 제거됨
import '../providers/unified_workspace_provider.dart';
import '../utils/logger_utils.dart';
import 'set_discount_provider.dart';
import '../repositories/set_discount_repository.dart';
import '../services/database_service.dart';

// 로컬 전용 모드: 사용하지 않는 import 제거됨

/// 카테고리 관련 액션을 처리하는 Provider
class CategoryNotifier extends StateNotifier<AsyncValue<List<Category>>> {
  final CategoryRepository _repository;
  final Ref _ref;
  // 로컬 전용 모드: 실시간 동기화 제거됨
  Timer? _debounceTimer;

  // 실시간 동기화 보호장치
  final Set<int> _recentlyAddedCategories = {}; // 최근 추가한 카테고리 ID 캐시 (무한 루프 방지)
  final Set<int> _recentlyUpdatedCategories = {}; // 최근 수정한 카테고리 ID 캐시 (무한 루프 방지)
  final Set<int> _recentlyDeletedCategories = {}; // 최근 삭제한 카테고리 ID 캐시 (무한 루프 방지)
  bool _isUploading = false; // 업로드 중 상태 관리

  CategoryNotifier(this._repository, this._ref) : super(const AsyncValue.loading()) {
    // 생성자에서 바로 loadCategories를 호출하지 않음
    // 대신 필요할 때 명시적으로 호출
    _watchCurrentEvent();
    _setupRealtimeSync();
  }

  /// 카테고리 목록 로드 (깜빡거림 방지를 위해 기존 데이터 유지)
  Future<void> loadCategories({int? eventId}) async {
    try {
      // 카테고리는 모든 플랜에서 로컬 사용 가능 (서버 동기화만 프로 플랜 제한)

      // 기존 데이터가 있으면 loading 상태로 변경하지 않음 (깜빡거림 방지)
      final hasExistingData = state.hasValue && state.value!.isNotEmpty;
      if (!hasExistingData) {
        state = const AsyncValue.loading();
      }

      await _repository.loadCategories(eventId: eventId);
      final categories = _repository.state.categories;
      state = AsyncValue.data(categories);
    } catch (e) {
      LoggerUtils.logError('카테고리 로딩 실패', error: e, tag: 'CategoryNotifier');
      state = AsyncValue.error(e, StackTrace.current);
    } finally {
      // 안전장치: 로딩 상태가 계속 유지되지 않도록 보장
      if (state.isLoading) {
        // 로딩 상태가 계속 유지되면 빈 데이터로라도 완료 처리
        state = const AsyncValue.data([]);
      }
    }
  }

  /// 카테고리 추가 (로컬 DB + Firebase 동기화 포함)
  /// 반환값: {success: bool, errorMessage: String?}
  Future<Map<String, dynamic>> addCategory({
    required String name,
    int? eventId,
    int? sortOrder,
    int? color,
  }) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 새 추가 요청 무시', tag: 'CategoryNotifier');
        return {'success': false, 'errorMessage': '카테고리 업로드가 진행 중입니다. 잠시 후 다시 시도해주세요.'};
      }

      _isUploading = true;

      final result = await _repository.addCategory(
        name: name,
        eventId: eventId,
        sortOrder: sortOrder,
        color: color,
      );

      if (result != null) {
        // 최근 추가한 카테고리로 캐시 (무한 루프 방지용)
        if (result.id != null) {
          _recentlyAddedCategories.add(result.id!);
          // 5초 후 캐시에서 제거
          Future.delayed(const Duration(seconds: 5), () {
            _recentlyAddedCategories.remove(result.id!);
          });
        }

        // 성공시 목록 새로고침
        await loadCategories(eventId: eventId);

        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 카테고리 서버 업로드 건너뜀: ${result.name}', tag: 'CategoryNotifier');

        return {'success': true, 'errorMessage': null};
      } else {
        // Repository에서 설정한 구체적인 에러 메시지 가져오기
        final errorMessage = _repository.state.error ?? '카테고리 추가에 실패했습니다.';
        return {'success': false, 'errorMessage': errorMessage};
      }
    } catch (e) {
      LoggerUtils.logError('Failed to add category', error: e, tag: 'CategoryNotifier');
      String errorMessage = '카테고리 추가 중 오류가 발생했습니다.';

      // 구체적인 에러 메시지 추출
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('이미 존재하는') || errorString.contains('중복')) {
        errorMessage = '이미 존재하는 카테고리 이름입니다.';
      } else if (errorString.contains('database')) {
        errorMessage = '데이터베이스 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
      }

      return {'success': false, 'errorMessage': errorMessage};
    } finally {
      _isUploading = false;
    }
  }



  /// 카테고리 수정 (로컬 DB + Firebase 동기화 포함)
  /// 반환값: {success: bool, errorMessage: String?}
  Future<Map<String, dynamic>> updateCategory({
    required int id,
    String? name,
    int? sortOrder,
    int? color,
  }) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 수정 요청 무시', tag: 'CategoryNotifier');
        return {'success': false, 'errorMessage': '카테고리 업데이트가 진행 중입니다. 잠시 후 다시 시도해주세요.'};
      }

      _isUploading = true;

      // 수정 전 카테고리 정보 가져오기 (실시간 동기화용)
      final originalCategory = await _repository.getCategoryById(id);

      final result = await _repository.updateCategory(
        id: id,
        name: name,
        sortOrder: sortOrder,
        color: color,
      );

      if (result && originalCategory != null) {
        // 최근 수정한 카테고리로 캐시 (무한 루프 방지용)
        _recentlyUpdatedCategories.add(id);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyUpdatedCategories.remove(id);
        });

        // 성공시 목록 새로고침
        await loadCategories();

        // 로컬 전용 모드: 실시간 동기화 불필요
        final updatedCategory = originalCategory.copyWith(
          name: name ?? originalCategory.name,
          sortOrder: sortOrder ?? originalCategory.sortOrder,
          color: color ?? originalCategory.color,
        );
        LoggerUtils.logInfo('로컬 전용 모드: 카테고리 수정 서버 업로드 건너뜀: ${updatedCategory.name}', tag: 'CategoryNotifier');

        return {'success': true, 'errorMessage': null};
      } else {
        // Repository에서 설정한 구체적인 에러 메시지 가져오기
        final errorMessage = _repository.state.error ?? '카테고리 수정에 실패했습니다.';
        return {'success': false, 'errorMessage': errorMessage};
      }
    } catch (e) {
      LoggerUtils.logError('Failed to update category', error: e, tag: 'CategoryNotifier');
      String errorMessage = '카테고리 수정 중 오류가 발생했습니다.';

      // 구체적인 에러 메시지 추출
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('이미 존재하는') || errorString.contains('중복')) {
        errorMessage = '이미 존재하는 카테고리 이름입니다.';
      } else if (errorString.contains('database')) {
        errorMessage = '데이터베이스 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
      }

      return {'success': false, 'errorMessage': errorMessage};
    } finally {
      _isUploading = false;
    }
  }



  /// 카테고리 삭제
  Future<bool> deleteCategory(int id) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 삭제 요청 무시', tag: 'CategoryNotifier');
        return false;
      }

      _isUploading = true;

      // 삭제 전 카테고리 정보 가져오기 (실시간 동기화용)
      final categoryToDelete = await _repository.getCategoryById(id);
      
      final result = await _repository.deleteCategory(id);

      if (result && categoryToDelete != null) {
        // 관련 세트 할인 삭제
        await _deleteRelatedSetDiscounts(id);

        // 최근 삭제한 카테고리로 캐시 (무한 루프 방지용)
        _recentlyDeletedCategories.add(id);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyDeletedCategories.remove(id);
        });

        // 성공시 목록 새로고침
        await loadCategories();

        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 카테고리 삭제 서버 동기화 건너뜀: ${categoryToDelete.name}', tag: 'CategoryNotifier');

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to delete category', error: e, tag: 'CategoryNotifier');
      return false;
    } finally {
      _isUploading = false;
    }
  }

  /// 카테고리 삭제 시 관련 세트 할인 삭제
  Future<void> _deleteRelatedSetDiscounts(int categoryId) async {
    try {
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) return;

      final setDiscountRepository = SetDiscountRepository(database: _ref.read(databaseServiceProvider));
      final relatedSetDiscounts = await setDiscountRepository.getSetDiscountsByCategoryId(
        categoryId,
        eventId: currentWorkspace.id,
      );

      if (relatedSetDiscounts.isNotEmpty) {
        final setDiscountNotifier = _ref.read(setDiscountNotifierProvider.notifier);
        int deletedCount = 0;

        for (final setDiscount in relatedSetDiscounts) {
          if (setDiscount.id != null) {
            final success = await setDiscountNotifier.deleteSetDiscount(setDiscount.id!);
            if (success) {
              deletedCount++;
              LoggerUtils.logInfo('카테고리별 수량 할인 삭제됨: ${setDiscount.name}', tag: 'CategoryNotifier');
            }
          }
        }

        if (deletedCount > 0) {
          LoggerUtils.logInfo('카테고리 삭제로 인해 ${deletedCount}개의 카테고리별 수량 할인이 삭제되었습니다.', tag: 'CategoryNotifier');
        }
      }
    } catch (e) {
      LoggerUtils.logError('관련 세트 할인 삭제 실패', error: e, tag: 'CategoryNotifier');
    }
  }

  /// 카테고리 순서 재정렬
  Future<bool> reorderCategories(List<Category> reorderedCategories) async {
    try {
      final result = await _repository.reorderCategories(reorderedCategories);

      if (result) {
        // 성공시 목록 새로고침
        await loadCategories();

        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 카테고리 순서 변경 서버 동기화 건너뜀: ${reorderedCategories.length}개', tag: 'CategoryNotifier');

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to reorder categories', error: e, tag: 'CategoryNotifier');
      return false;
    }
  }

  /// 특정 카테고리 조회
  Future<Category?> getCategoryById(int id) async {
    try {
      return await _repository.getCategoryById(id);
    } catch (e) {
      LoggerUtils.logError('Failed to get category by id', error: e, tag: 'CategoryNotifier');
      return null;
    }
  }

  /// 이벤트에 기본 카테고리 생성
  Future<void> createDefaultCategoryForEvent(int eventId) async {
    try {
      await _repository.createDefaultCategoryForEvent(eventId);
      await loadCategories(eventId: eventId);
    } catch (e) {
      LoggerUtils.logError('Failed to create default category', error: e, tag: 'CategoryNotifier');
    }
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    _ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - CategoryNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: 'CategoryNotifier');

        if (next != null) {
          // 이전 데이터를 유지하면서 새 데이터 로딩 (깜빡거림 방지)
          // state = const AsyncValue.loading(); // 이 줄을 제거하여 깜빡거림 방지

          // 즉시 카테고리 로딩 (지연 제거)
          loadCategories(eventId: next.id);

          _setupRealtimeSync(); // 새 워크스페이스에 대한 실시간 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 카테고리 목록 클리어
          state = const AsyncValue.data([]);
        }
      }
    });
  }

  /// 실시간 동기화 설정
  // 로컬 전용 모드: 실시간 동기화 기능 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드: 실시간 동기화 제거됨
  }

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  @override
  void dispose() {
    // 로컬 전용 모드: 실시간 동기화 구독 제거됨
    _debounceTimer?.cancel();

    // 캐시 정리
    _recentlyAddedCategories.clear();
    _recentlyUpdatedCategories.clear();
    _recentlyDeletedCategories.clear();

    super.dispose();
  }

  /// 현재 에러 메시지 가져오기
  String? get errorMessage => _repository.state.error;

  /// 로딩 상태 확인
  bool get isLoading => _repository.state.isLoading;
}

/// 카테고리 Provider
final categoryNotifierProvider = StateNotifierProvider<CategoryNotifier, AsyncValue<List<Category>>>((ref) {
  final repository = ref.watch(categoryRepositoryProvider.notifier);
  return CategoryNotifier(repository, ref);
});

/// 현재 카테고리 목록을 제공하는 Provider
final currentCategoriesProvider = Provider<List<Category>>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    data: (categories) => categories,
    orElse: () => [],
  );
});

/// 카테고리 로딩 상태를 제공하는 Provider
final categoryLoadingStateProvider = Provider<bool>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    loading: () => true,
    orElse: () => false,
  );
});

/// 카테고리 에러 상태를 제공하는 Provider
final categoryErrorStateProvider = Provider<String?>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    error: (error, _) => error.toString(),
    orElse: () => null,
  );
});

/// 특정 ID의 카테고리를 가져오는 Provider
final categoryByIdProvider = Provider.family<Category?, int>((ref, id) {
  final categories = ref.watch(currentCategoriesProvider);
  try {
    return categories.firstWhere((category) => category.id == id);
  } catch (e) {
    return null;
  }
});

/// 카테고리 개수를 제공하는 Provider
final categoryCountProvider = Provider<int>((ref) {
  final categories = ref.watch(currentCategoriesProvider);
  return categories.length;
});
