import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/event.dart';
import '../models/event_state.dart';
import '../models/event_sort_option.dart';

import '../repositories/event_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/change_detection_utils.dart';
import '../services/event_workspace_manager.dart';
import 'unified_workspace_provider.dart';
import '../utils/event_workspace_utils.dart';
import 'seller_provider.dart';

import 'revenue_goal_provider.dart';
import 'sales_log_provider.dart';
import 'prepayment_provider.dart';
import 'checklist_provider.dart';
import 'category_provider.dart';
import 'product_provider.dart';
import 'home_dashboard_filter_provider.dart';

/// Event 상태를 관리하는 StateNotifier입니다.
///
/// 주요 기능:
/// - 행사 목록 관리
/// - CRUD 작업
/// - 검색 및 필터링
/// - 현재 선택된 행사 관리
class EventNotifier extends StateNotifier<EventState> {
  static const String _tag = 'EventProvider';

  final EventRepository _repository;
  final Ref _ref;



  EventNotifier(this._repository, this._ref) : super(const EventState()) {
    _initializeEvents();
  }

  /// 초기 행사 데이터를 로드합니다.
  Future<void> _initializeEvents() async {
    try {
      LoggerUtils.methodStart('_initializeEvents', tag: _tag);

      // 행사 목록 로드
      await loadEvents();

      // 현재 선택된 행사 설정
      await _setInitialCurrentEvent();

      LoggerUtils.methodEnd('_initializeEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '초기 행사 데이터 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 데이터를 불러오는데 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 초기 현재 행사를 설정합니다.
  Future<void> _setInitialCurrentEvent() async {
    try {
      final currentWorkspace = _ref.read(currentWorkspaceProvider);

      // 이미 현재 워크스페이스가 설정되어 있으면 덮어쓰지 않음 (온보딩 완료 후 보존)
      if (currentWorkspace != null) {
        return;
      }

      // 마지막 사용한 행사로 설정 (SharedPreferences에서 복원)
      // 이 로직은 이미 unified_workspace_provider에서 처리됨
      LoggerUtils.logInfo('마지막 사용한 행사는 워크스페이스 프로바이더에서 복원됨', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초기 현재 행사 설정 실패', tag: _tag, error: e);
    }
  }

  // 로컬 전용 모드: 실시간 동기화 메서드 완전 삭제

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  /// EventNotifier dispose 처리
  @override
  void dispose() {
    super.dispose();
  }

  /// 행사 목록을 로드합니다.
  Future<void> loadEvents({
    bool showLoading = true,
    EventFilter? filter,
  }) async {
    try {
      LoggerUtils.methodStart('loadEvents', tag: _tag);

      if (showLoading) {
        state = state.copyWith(isLoading: true, errorMessage: null);
      } else {
        state = state.copyWith(isRefreshing: true, errorMessage: null);
      }

      final filterToUse = filter ?? state.currentFilter;
      final events = await _repository.getAllEvents(filter: filterToUse);

      state = state.copyWith(
        events: events,
        isLoading: false,
        isRefreshing: false,
        currentFilter: filterToUse,
        lastUpdated: DateTime.now(),
        totalCount: events.length,
        filteredCount: events.length,
        errorMessage: null,
      );

      // 행사 목록 로드 후 현재 행사 초기화
      await _setInitialCurrentEvent();

      LoggerUtils.logInfo('행사 ${events.length}개 로드 완료', tag: _tag);
      LoggerUtils.methodEnd('loadEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 목록 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );

      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        errorMessage: '행사 목록을 불러오는데 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 새로운 행사를 추가합니다.
  Future<Event?> addEvent(Event event) async {
    try {
      LoggerUtils.methodStart('addEvent', tag: _tag, data: {'name': event.name});

      state = state.copyWith(isLoading: true, errorMessage: null);

      final addedEvent = await _repository.insertEvent(event);

      // 상태 업데이트
      final updatedEvents = [...state.events, addedEvent];
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        totalCount: updatedEvents.length,
        filteredCount: updatedEvents.length,
      );

      // 로컬 전용 모드: Firebase 동기화 제거
      LoggerUtils.logInfo('새 행사 로컬 저장 완료: ${addedEvent.name}', tag: _tag);

      LoggerUtils.logInfo('행사 추가 성공: ${addedEvent.name} (ID: ${addedEvent.id})', tag: _tag);
      LoggerUtils.methodEnd('addEvent', tag: _tag);

      return addedEvent; // 생성된 행사 반환
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 추가 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'name': event.name},
      );

      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 추가에 실패했습니다: ${e.toString()}',
      );

      return null; // 실패 시 null 반환
    }
  }



  /// 행사 정보를 업데이트합니다.
  Future<void> updateEvent(Event event) async {
    try {
      LoggerUtils.methodStart('updateEvent', tag: _tag, data: {'id': event.id, 'name': event.name});

      state = state.copyWith(isLoading: true, errorMessage: null);

      final updatedEvent = await _repository.updateEvent(event);

      // 상태 업데이트
      final updatedEvents = state.events.map((e) {
        return e.id == updatedEvent.id ? updatedEvent : e;
      }).toList();

      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        selectedEvent: state.selectedEvent?.id == updatedEvent.id ? updatedEvent : state.selectedEvent,
      );

      // 현재 선택된 행사가 업데이트된 경우 WorkspaceProvider도 업데이트
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == updatedEvent.id) {
        final updatedWorkspace = EventWorkspaceUtils.eventToWorkspace(updatedEvent);
        if (updatedWorkspace != null) {
          await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(updatedWorkspace);
        }
      }

      // 관련 Provider들에게 행사 업데이트 알림
      await _notifyRelatedProviders(updatedEvent);

      // 로컬 전용 모드: 실시간 동기화 관련 코드 제거됨

      LoggerUtils.logInfo('행사 업데이트 성공: ${updatedEvent.name}', tag: _tag);
      LoggerUtils.methodEnd('updateEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': event.id, 'name': event.name},
      );

      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 업데이트에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 행사의 특정 필드만 업데이트합니다 (선택적 업데이트)
  Future<void> updateEventFields(int eventId, Map<String, dynamic> fields) async {
    try {
      LoggerUtils.methodStart('updateEventFields', tag: _tag, data: {'id': eventId, 'fields': fields.keys.toList()});

      state = state.copyWith(isLoading: true, errorMessage: null);

      // 현재 이벤트 조회
      final currentEvent = state.events.firstWhere((e) => e.id == eventId);

      // 세밀한 변경 감지를 통해 실제 변경된 필드만 추출
      final currentEventMap = ChangeDetectionUtils.objectToMap(currentEvent);
      final actualChangedFields = ChangeDetectionUtils.detectFieldChanges(
        currentEventMap,
        {...currentEventMap, ...fields},
        excludeFields: ['updatedAt'], // 자동 업데이트되는 필드 제외
      );

      if (actualChangedFields.isEmpty) {
        LoggerUtils.logInfo('실제 변경된 필드가 없어 업데이트를 건너뜁니다', tag: _tag);
        state = state.copyWith(isLoading: false);
        return;
      }

      LoggerUtils.logInfo('실제 변경된 필드: ${actualChangedFields.keys.join(', ')}', tag: _tag);

      // 필드 업데이트를 위한 copyWith 호출
      Event updatedEvent = currentEvent;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('revenueGoalMode')) {
        updatedEvent = updatedEvent.copyWith(
          revenueGoalMode: fields['revenueGoalMode'] as RevenueGoalMode,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('name')) {
        updatedEvent = updatedEvent.copyWith(
          name: fields['name'] as String,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('description')) {
        updatedEvent = updatedEvent.copyWith(
          description: fields['description'] as String?,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('imagePath')) {
        updatedEvent = updatedEvent.copyWith(
          imagePath: fields['imagePath'] as String?,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('startDate')) {
        updatedEvent = updatedEvent.copyWith(
          startDate: fields['startDate'] as DateTime,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('endDate')) {
        updatedEvent = updatedEvent.copyWith(
          endDate: fields['endDate'] as DateTime,
          updatedAt: DateTime.now(),
        );
      }

      if (fields.containsKey('isActive')) {
        updatedEvent = updatedEvent.copyWith(
          isActive: fields['isActive'] as bool,
          updatedAt: DateTime.now(),
        );
      }

      // 로컬 DB 업데이트
      final finalUpdatedEvent = await _repository.updateEvent(updatedEvent);

      // 상태 업데이트
      final updatedEvents = state.events.map((e) {
        return e.id == finalUpdatedEvent.id ? finalUpdatedEvent : e;
      }).toList();

      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        selectedEvent: state.selectedEvent?.id == finalUpdatedEvent.id ? finalUpdatedEvent : state.selectedEvent,
      );

      // 현재 선택된 행사가 업데이트된 경우 WorkspaceProvider도 업데이트
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == finalUpdatedEvent.id) {
        final updatedWorkspace = EventWorkspaceUtils.eventToWorkspace(finalUpdatedEvent);
        if (updatedWorkspace != null) {
          await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(updatedWorkspace);
        }
      }

      // 워크스페이스 매니저 최신화 (현재 워크스페이스 날짜 교체)
      await EventWorkspaceManager.instance.refreshWorkspaces();

      // 관련 Provider들에게 행사 업데이트 알림
      await _notifyRelatedProviders(finalUpdatedEvent);

      // 로컬 전용 모드: 실시간 동기화 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 행사 필드 업데이트 서버 업로드 건너뜀: ${actualChangedFields.keys.join(', ')}', tag: _tag);

      LoggerUtils.logInfo('행사 필드 업데이트 성공: ${fields.keys.join(', ')}', tag: _tag);
      LoggerUtils.methodEnd('updateEventFields', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 필드 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': eventId, 'fields': fields.keys.toList()},
      );

      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 필드 업데이트에 실패했습니다: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// 행사를 삭제합니다.
  Future<void> deleteEvent(int id) async {
    try {
      LoggerUtils.methodStart('deleteEvent', tag: _tag, data: {'id': id});

      state = state.copyWith(isLoading: true, errorMessage: null);

      // 삭제할 행사 정보 미리 저장 (실시간 동기화용)
      final eventToDelete = state.events.firstWhere((e) => e.id == id);

      await _repository.deleteEvent(id);

      // 상태 업데이트
      final updatedEvents = state.events.where((e) => e.id != id).toList();
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        totalCount: updatedEvents.length,
        filteredCount: updatedEvents.length,
        selectedEvent: state.selectedEvent?.id == id ? null : state.selectedEvent,
      );

      // 현재 선택된 행사가 삭제된 경우 다른 행사로 변경
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == id) {
        // 남은 행사 중 첫 번째 행사로 변경
        if (updatedEvents.isNotEmpty) {
          final firstEvent = updatedEvents.first;
          final firstWorkspace = EventWorkspaceUtils.eventToWorkspace(firstEvent);
          if (firstWorkspace != null) {
            await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(firstWorkspace);
          }
        }
      }

      // 로컬 전용 모드: 실시간 동기화 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 행사 삭제 서버 동기화 건너뜀: ${eventToDelete.name}', tag: _tag);

      LoggerUtils.logInfo('행사 삭제 성공: ID $id', tag: _tag);
      LoggerUtils.methodEnd('deleteEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 삭제 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': id},
      );

      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 삭제에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 행사를 강제로 삭제합니다. (리셋 시 사용: 마지막 행사도 삭제 허용)
  Future<void> deleteEventForce(int id) async {
    try {
      LoggerUtils.methodStart('deleteEventForce', tag: _tag, data: {'id': id});

      state = state.copyWith(isLoading: true, errorMessage: null);

      // 강제 삭제 (레포지토리 강제 삭제 호출)
      await _repository.deleteEventForce(id);

      // 상태 업데이트
      final updatedEvents = state.events.where((e) => e.id != id).toList();
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        totalCount: updatedEvents.length,
        filteredCount: updatedEvents.length,
        selectedEvent: state.selectedEvent?.id == id ? null : state.selectedEvent,
      );

      // 현재 선택된 행사가 삭제된 경우 워크스페이스 정리
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == id) {
        if (updatedEvents.isNotEmpty) {
          final firstEvent = updatedEvents.first;
          final firstWorkspace = EventWorkspaceUtils.eventToWorkspace(firstEvent);
          if (firstWorkspace != null) {
            await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(firstWorkspace);
          }
        }
      }

      // 로컬 전용 모드: 실시간 동기화 불필요

      LoggerUtils.logInfo('행사 강제 삭제 성공: ID $id', tag: _tag);
      LoggerUtils.methodEnd('deleteEventForce', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 강제 삭제 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': id},
      );
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 삭제에 실패했습니다: ${e.toString()}',
      );
      rethrow;
    }
  }


  /// 행사를 검색합니다.
  Future<void> searchEvents(String keyword) async {
    try {
      LoggerUtils.methodStart('searchEvents', tag: _tag, data: {'keyword': keyword});

      final filter = state.currentFilter.copyWith(searchKeyword: keyword);

      state = state.copyWith(
        isSearchMode: keyword.isNotEmpty,
        currentFilter: filter,
      );

      await loadEvents(showLoading: false, filter: filter);

      LoggerUtils.methodEnd('searchEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 검색 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'keyword': keyword},
      );
    }
  }

  /// 필터를 적용합니다.
  Future<void> applyFilter(EventFilter filter) async {
    try {
      LoggerUtils.methodStart('applyFilter', tag: _tag);

      await loadEvents(showLoading: false, filter: filter);

      LoggerUtils.methodEnd('applyFilter', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '필터 적용 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 정렬 옵션을 변경합니다.
  Future<void> changeSortOption(EventSortOption sortOption) async {
    final filter = state.currentFilter.copyWith(sortOption: sortOption);
    await applyFilter(filter);
  }

  /// 행사를 선택합니다.
  void selectEvent(Event? event) {
    state = state.copyWith(selectedEvent: event);
  }

  /// 현재 선택된 행사를 설정합니다.
  Future<void> setCurrentEvent(Event event) async {
    try {
      final workspace = EventWorkspaceUtils.eventToWorkspace(event);
      if (workspace != null) {
        await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(workspace);
        LoggerUtils.logInfo('현재 행사를 워크스페이스로 변경: ${event.name}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('현재 행사 설정 실패', tag: _tag, error: e);
    }
  }

  /// 에러를 클리어합니다.
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 검색 모드를 해제합니다.
  Future<void> clearSearch() async {
    final filter = state.currentFilter.copyWith(searchKeyword: '');
    state = state.copyWith(isSearchMode: false);
    await applyFilter(filter);
  }

  /// 필터를 초기화합니다.
  Future<void> resetFilter() async {
    await applyFilter(EventFilter.defaultFilter);
  }

  /// 새로고침합니다.
  Future<void> refresh() async {
    await loadEvents(showLoading: false);
  }

  /// 관련 Provider들에게 행사 업데이트 알림
  Future<void> _notifyRelatedProviders(Event updatedEvent) async {
    try {
      // 현재 선택된 행사가 업데이트된 경우에만 관련 Provider들 갱신
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == updatedEvent.id) {
        // 즉시 UI 갱신을 위한 강력한 새로고침 (백그라운드가 아닌 즉시 실행)
        try {
          // 현재 워크스페이스 Provider 강제 갱신 (홈 대시보드 날짜 범위 등에 필요)
          _ref.invalidate(currentWorkspaceProvider);

          // 홈 대시보드 날짜 범위 Provider 갱신 (날짜 선택 다이얼로그에 필요)
          _ref.invalidate(homeDashboardDateRangeProvider);

          // 통계 관련 Provider들 갱신
          _ref.invalidate(salesStatsProvider);
          _ref.invalidate(revenueGoalStatsProvider);

          // 짧은 지연 후 Provider들 직접 새로고침
          await Future.delayed(const Duration(milliseconds: 50));

          // 목표 수익 Provider 갱신
          await _ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);

          // 판매 기록 Provider 갱신
          await _ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

          // 선입금 Provider 갱신
          await _ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

          // 판매자 Provider 갱신
          await _ref.read(sellerNotifierProvider.notifier).loadSellers();

          // 체크리스트 Provider 갱신
          await _ref.read(checklistNotifierProvider.notifier).loadData();

          // 카테고리 Provider 갱신 (상품 관련 통계에 영향)
          await _ref.read(categoryNotifierProvider.notifier).loadCategories(eventId: updatedEvent.id!);

          // 상품 Provider 갱신 (재고 및 통계에 영향)
          await _ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

          LoggerUtils.logInfo('관련 Provider들 즉시 갱신 완료: ${updatedEvent.name}', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('관련 Provider 즉시 갱신 실패', tag: _tag, error: e);
        }
      }
    } catch (e) {
      LoggerUtils.logError('관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }
}

/// EventProvider
final eventNotifierProvider = StateNotifierProvider<EventNotifier, EventState>((ref) {
  final repository = ref.read(eventRepositoryProvider);
  return EventNotifier(repository, ref);
});

/// 현재 행사 목록만 필요한 경우를 위한 Provider
final eventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.events;
});

/// 진행 중인 행사 목록 Provider
final ongoingEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.ongoingEvents;
});

/// 예정된 행사 목록 Provider
final upcomingEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.upcomingEvents;
});

/// 종료된 행사 목록 Provider
final endedEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.endedEvents;
});

/// 행사 로딩 상태 Provider
final eventLoadingProvider = Provider<bool>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.isLoading;
});

/// 행사 에러 상태 Provider
final eventErrorProvider = Provider<String?>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.errorMessage;
});
