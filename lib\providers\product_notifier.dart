import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import '../models/product.dart';
import '../models/product_sort_option.dart';
import '../models/sales_log.dart';
import '../repositories/product_repository.dart';
import '../repositories/sales_log_repository.dart';

import '../utils/logger_utils.dart';
import '../mixins/workspace_aware_provider_mixin.dart';

import 'prepayment_product_link_provider.dart';
import 'product_state.dart';
import '../models/event_workspace.dart';
import 'unified_workspace_provider.dart';
import 'settings_provider.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨
import '../services/database_service.dart';


import 'sales_log_provider.dart';
import 'prepayment_provider.dart';
import 'set_discount_provider.dart';
import '../repositories/set_discount_repository.dart';
// 로컬 전용 모드: 구독 관련 import 제거됨


/// 상품 데이터베이스 접근을 위한 Repository Provider는 product_provider.dart에서 정의됨

class ProductNotifier extends StateNotifier<ProductState> with WorkspaceAwareProviderMixin<ProductState> {
  final ProductRepository repository;
  @override
  final Ref ref;
  final bool autoInit;
  String _searchQuery = '';
  Timer? _debounceTimer;
  bool _isPaused = false;
  // 로컬 전용 모드: 실시간 동기화 제거됨
  static const String _pageKey = 'product_management';

  @override
  String get providerTag => 'ProductNotifier';

  ProductNotifier(this.repository, this.ref, {this.autoInit = true}) : super(ProductState.initialState()) {
    // 즉시 로딩하지 않고 워크스페이스가 설정된 후에만 로딩
    watchCurrentEvent(); // Mixin의 메서드 사용
    _setupDirectFirestoreSync(); // 새로운 직접 실시간 동기화

    // autoInit이 true이고 currentWorkspace가 이미 설정되어 있는 경우에만 로딩
    if (autoInit) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        if (currentWorkspace != null) {
          _restoreSettings(); // 설정 복원
          loadProducts(showLoading: false);
        }
      });
    }
  }

  /// Mixin에서 요구하는 워크스페이스 변경 처리 메서드
  @override
  Future<void> onWorkspaceChanged(EventWorkspace? workspace) async {
    if (workspace != null) {
      await loadProducts(showLoading: false);
      _setupRealtimeSync(); // 실시간 동기화 활성화
      _setupDirectFirestoreSync(); // 새 워크스페이스에 대한 직접 Firestore 동기화 재설정
    } else {
      // 현재 행사 워크스페이스가 null이 되면 상품 목록 클리어
      state = state.copyWith(
        products: [],
        filteredProducts: [],
        errorMessage: getNoWorkspaceErrorMessage(),
      );
    }
  }

  /// Mixin에서 요구하는 메모리 정리 메서드
  @override
  void clearDataForEventTransition() {
    _clearAllDataForEventTransition();
  }

  /// Mixin에서 요구하는 실시간 동기화 설정 메서드
  @override
  void setupRealtimeSync() {
    _setupRealtimeSync();
  }

  /// 로컬 전용 모드: 실시간 동기화 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드에서는 실시간 동기화를 사용하지 않음
  }

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  /// 행사 전환 시 메모리 완전 클리어 (메모리 누수 방지)
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 시작', tag: 'ProductNotifier');

      // 로컬 전용 모드: 실시간 구독 해제 제거됨

      // 직접 Firestore 구독은 더 이상 사용하지 않음 (중복 구독 방지)

      try {
        _debounceTimer?.cancel();
        _debounceTimer = null;
      } catch (e) {
        LoggerUtils.logError('디바운스 타이머 해제 실패', tag: 'ProductNotifier', error: e);
      }

      // 2. 상태를 로딩 상태로만 변경 (데이터는 유지하여 이미지 깜빡거림 방지)
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
      );

      // 3. 내부 변수 클리어
      _searchQuery = '';

      LoggerUtils.logInfo('행사 전환 - 로딩 상태로 변경 완료 (데이터 유지)', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('메모리 클리어 중 오류', tag: 'ProductNotifier', error: e);
    }
  }

  /// 직접 Firestore 실시간 동기화 설정 (Local-First 방식)
  void _setupDirectFirestoreSync() {
    // 중복 구독 방지: RealtimeSyncService에서 이미 products 컬렉션을 구독하고 있음
    // 직접 Firestore 구독을 비활성화하여 읽기 사용량 최적화

    // 직접 Firestore 구독은 더 이상 사용하지 않음

    // RealtimeSyncService를 통해서만 데이터를 받도록 함
    // _setupRealtimeSync()에서 이미 처리됨
  }

  // 직접 Firestore 구독 관련 메서드들은 중복 구독 방지를 위해 제거됨
  // RealtimeSyncService를 통해서만 데이터를 받도록 변경





  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      LoggerUtils.logDebug('ProductNotifier paused', tag: 'ProductNotifier');
    }
  }

  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('ProductNotifier resumed', tag: 'ProductNotifier');
      loadProducts(showLoading: false);
    }
  }

  /// 상품 목록을 로드합니다. (서버 fallback 포함)
  Future<void> loadProducts({bool showLoading = true}) async {
    LoggerUtils.methodStart('loadProducts', tag: 'ProductNotifier');

    // 상품 관리는 모든 플랜에서 로컬 사용 가능 (서버 동기화만 프로 플랜 제한)

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }

    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'ProductNotifier');
        state = state.copyWith(
          products: [],
          filteredProducts: [],
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
        );
        return;
      }

      // 성능 최적화: 병렬 처리 및 캐싱
      final stopwatch = Stopwatch()..start();

      // 1단계: 로컬 DB에서 상품 로드
      var products = await repository.getProductsByEventId(currentWorkspace.id);

      // 2단계: 로컬에 데이터가 없으면 안내 메시지 표시 (서버 다운로드는 DataSyncService에서만 담당)
      if (products.isEmpty) {
        LoggerUtils.logInfo('로컬에 상품 데이터가 없음 - DataSyncService에서 다운로드 대기 중', tag: 'ProductNotifier');
        // 서버 다운로드는 AppWrapper의 차분 동기화나 전체 다운로드에서 처리됨
        // ProductNotifier는 로컬 데이터 관리에만 집중하여 중복 다운로드 방지
      }

      if (!mounted) return;

      // 상태 업데이트 최적화
      state = state.copyWith(
        products: products,
        filteredProducts: products,
        isLoading: false,
        errorMessage: null,
      );

      // 판매자 이름 목록 업데이트
      final sellerNames = products
          .map((p) => p.sellerName)
          .where((name) => name != null && name.isNotEmpty)
          .map((name) => name!)
          .toSet()
          .toList()
        ..sort();

      state = state.copyWith(sellerNames: sellerNames);

      // 정렬 및 필터 적용 (품절 상품을 뒤로 밀기 포함)
      _applySortAndFilters();

      stopwatch.stop();
      LoggerUtils.logInfo('상품 로딩 완료: ${products.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms', tag: 'ProductNotifier');

    } catch (e) {
      LoggerUtils.logError('상품 로딩 실패', tag: 'ProductNotifier', error: e);
      if (!mounted) return;
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        errorCode: 'PRD_LOAD_ERROR',
        errorSeverity: 'high',
      );
    } finally {
      // 안전장치: 어떤 경우든 로딩 상태를 false로 설정
      if (mounted && state.isLoading) {
        state = state.copyWith(isLoading: false);
      }
      LoggerUtils.methodEnd('loadProducts', tag: 'ProductNotifier');
    }
  }

  /// 상품 추가 (로컬 DB + 선택적 Firebase 동기화 포함)
  Future<void> addProduct(Product product, {bool skipFirebase = false}) async {
    LoggerUtils.methodStart('addProduct', tag: 'ProductNotifier');
    try {
      // 현재 선택된 행사 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logError('현재 워크스페이스가 null입니다', tag: 'ProductNotifier');
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '행사 워크스페이스를 선택해주세요',
          errorCode: 'PRD_NO_WORKSPACE_ERROR',
          errorSeverity: 'high',
        );
        throw Exception('현재 워크스페이스가 선택되지 않았습니다');
      }

      LoggerUtils.logInfo('상품 등록 시작 - 워크스페이스: ${currentWorkspace.name} (ID: ${currentWorkspace.id}), 상품: ${product.name}, skipFirebase: $skipFirebase', tag: 'ProductNotifier');

      // 상품에 현재 워크스페이스 ID 설정
      final productWithEventId = product.copyWith(eventId: currentWorkspace.id);

      LoggerUtils.logInfo('상품 등록 데이터 - 이름: ${productWithEventId.name}, 가격: ${productWithEventId.price}, 수량: ${productWithEventId.quantity}, eventId: ${productWithEventId.eventId}', tag: 'ProductNotifier');

      // 1. 로컬 DB에 저장하고 생성된 ID 받기
      final insertedId = await repository.insertProduct(productWithEventId);
      LoggerUtils.logInfo('상품 로컬 저장 성공: ${productWithEventId.name}, 생성된 ID: $insertedId', tag: 'ProductNotifier');

      // 2. 생성된 ID를 포함한 Product 객체 생성
      final productWithId = productWithEventId.copyWith(id: insertedId);
      LoggerUtils.logInfo('ID가 포함된 상품 객체 생성: ${productWithId.name} (ID: ${productWithId.id})', tag: 'ProductNotifier');

      // 직접 Firestore 구독을 사용하지 않으므로 무한 루프 방지 캐시 불필요

      // 3. Firebase 업로드는 skipFirebase 파라미터와 플랜에 따라 결정 (다중 등록과 동일한 패턴)
      if (!skipFirebase) {
        // 로컬 전용 모드: 서버 동기화 제거됨
        LoggerUtils.logInfo('로컬 전용 모드: 상품 서버 동기화 건너뜀 - ${productWithId.name}', tag: 'ProductNotifier');
      } else {
        LoggerUtils.logInfo('Firebase 업로드 스킵 - 백그라운드에서 처리 예정: ${productWithId.name}', tag: 'ProductNotifier');
      }

      // 관련 Provider들에게 상품 추가 알림
      _notifyRelatedProviders(productWithId);

      if (!mounted) return;

      // 상품 등록 후 즉시 UI 갱신 (POS 화면에서 바로 사용 가능하도록)
      await loadProducts(showLoading: false);

      // 추가 안전장치: 상태 강제 갱신으로 POS 화면 실시간 반영 보장
      if (mounted) {
        // 미세한 지연 후 한 번 더 갱신 (DB 커밋 완료 보장)
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            loadProducts(showLoading: false);
          }
        });
      }
    } catch (e) {
      LoggerUtils.logError('상품 등록 실패', tag: 'ProductNotifier', error: e);
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_ADD_ERROR',
        errorSeverity: 'high',
      );
      rethrow; // 에러를 다시 던져서 호출자가 처리할 수 있도록 함
    }
    LoggerUtils.methodEnd('addProduct', tag: 'ProductNotifier');
  }



  /// 상품 수정 (로컬 DB + Firebase 동기화 + 연관 데이터 처리 포함)
  Future<void> updateProduct(Product product, {bool updateRelatedSalesLogs = true}) async {
    LoggerUtils.methodStart('updateProduct', tag: 'ProductNotifier');
    try {
      // 로컬 전용 모드: 상품명 변경 확인 제거됨

      // 1. 로컬 DB 업데이트
      await repository.updateProduct(product, updateRelatedSalesLogs: updateRelatedSalesLogs);

      // 2. Firebase에 즉시 업로드 (실시간 동기화 - 플랜별 제한)
      try {
        // 로컬 전용 모드: 서버 동기화 제거됨
        LoggerUtils.logInfo('로컬 전용 모드: 상품 서버 동기화 건너뜀 - ${product.name}', tag: 'ProductNotifier');
      } catch (e) {
        LoggerUtils.logError('상품 업데이트 처리 중 오류: ${product.name}', tag: 'ProductNotifier', error: e);
      }

      // 관련 Provider들에게 상품 업데이트 알림
      _notifyRelatedProviders(product);

      if (!mounted) return;
      await loadProducts(showLoading: false);
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_UPDATE_ERROR',
        errorSeverity: 'high',
      );
    }
    LoggerUtils.methodEnd('updateProduct', tag: 'ProductNotifier');
  }

  /// 상품의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updateProductFields(int productId, Map<String, dynamic> fields) async {
    try {
      LoggerUtils.methodStart('updateProductFields', tag: 'ProductNotifier', data: {'id': productId, 'fields': fields.keys.toList()});

      // 현재 상품 조회
      final currentProduct = await repository.getProductById(productId);
      if (currentProduct == null) {
        throw Exception('상품을 찾을 수 없습니다: $productId');
      }

      // 필드 업데이트를 위한 copyWith 호출
      Product updatedProduct = currentProduct;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('name')) {
        updatedProduct = updatedProduct.copyWith(name: fields['name'] as String);
      }

      if (fields.containsKey('price')) {
        updatedProduct = updatedProduct.copyWith(price: fields['price'] as int);
      }

      if (fields.containsKey('quantity')) {
        updatedProduct = updatedProduct.copyWith(quantity: fields['quantity'] as int);
      }

      if (fields.containsKey('sellerName')) {
        final sellerName = fields['sellerName'] as String?;
        updatedProduct = updatedProduct.copyWith(sellerName: sellerName);
      }

      if (fields.containsKey('imagePath')) {
        final imagePath = fields['imagePath'] as String?;
        updatedProduct = updatedProduct.copyWith(imagePath: imagePath);
      }

      if (fields.containsKey('isActive')) {
        updatedProduct = updatedProduct.copyWith(isActive: fields['isActive'] as bool);
      }

      if (fields.containsKey('categoryId')) {
        updatedProduct = updatedProduct.copyWith(categoryId: fields['categoryId'] as int);
      }

      // 로컬 DB 업데이트
      await repository.updateProduct(updatedProduct, updateRelatedSalesLogs: fields.containsKey('name'));

      // 로컬 전용 모드: Firebase 업데이트 제거됨
      LoggerUtils.logInfo('로컬 전용 모드: 상품 필드 업데이트 Firebase 업로드 건너뜀: ${fields.keys.join(', ')}', tag: 'ProductNotifier');

      // 상품명이 변경된 경우 연관된 판매 기록들도 Firebase에 동기화
      if (fields.containsKey('name')) {
        try {
          await _syncRelatedSalesLogsToFirebase(productId, fields['name'] as String);
        } catch (e) {
          LoggerUtils.logError('연관 판매 기록 동기화 실패', tag: 'ProductNotifier', error: e);
        }
      }

      // 관련 Provider들에게 상품 업데이트 알림
      _notifyRelatedProviders(updatedProduct);

      // 상태 갱신
      if (mounted) {
        await loadProducts(showLoading: false);
      }

      LoggerUtils.methodEnd('updateProductFields', tag: 'ProductNotifier');
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '상품 필드 업데이트 실패',
        tag: 'ProductNotifier',
        error: e,
        stackTrace: stackTrace,
        data: {'id': productId, 'fields': fields.keys.toList()},
      );

      if (mounted) {
        state = state.copyWith(
          errorMessage: '상품 필드 업데이트에 실패했습니다: ${e.toString()}',
          errorCode: 'PRD_UPDATE_FIELDS_ERROR',
          errorSeverity: 'high',
        );
      }
      rethrow;
    }
  }

  /// 배치 상품 업데이트 (판매 처리 최적화용)
  Future<List<Product>> batchUpdateProducts(List<Product> products) async {
    LoggerUtils.methodStart('batchUpdateProducts', tag: 'ProductNotifier');
    try {
      LoggerUtils.logInfo('배치 상품 업데이트 시작: ${products.length}개', tag: 'ProductNotifier');

      // 1. 로컬 DB 배치 업데이트
      final updatedProducts = await repository.batchUpdateProductQuantities(products);

      if (updatedProducts.isEmpty) {
        LoggerUtils.logWarning('배치 상품 업데이트 실패: 업데이트된 상품 없음', tag: 'ProductNotifier');
        return [];
      }

      // 2. 상태 갱신 (효율적인 부분 갱신)
      if (mounted) {
        final currentState = state;
        final productMap = {for (var p in updatedProducts) p.id: p};
        final newProducts = currentState.products.map((p) {
          return productMap[p.id] ?? p;
        }).toList();

        state = currentState.copyWith(products: newProducts);
        LoggerUtils.logDebug('배치 상품 상태 갱신 완료: ${updatedProducts.length}개', tag: 'ProductNotifier');
      }

      // 3. 백그라운드에서 Firebase 배치 동기화
      _syncProductsBatchInBackground(updatedProducts);

      // 4. 관련 Provider들에게 배치 업데이트 알림
      if (updatedProducts.isNotEmpty) {
        // 첫 번째 상품을 대표로 알림 (배치 업데이트이므로)
        _notifyRelatedProviders(updatedProducts.first);
      }

      LoggerUtils.logInfo('배치 상품 업데이트 완료: ${updatedProducts.length}개', tag: 'ProductNotifier');
      return updatedProducts;
    } catch (e) {
      LoggerUtils.logError('배치 상품 업데이트 실패', tag: 'ProductNotifier', error: e);
      if (mounted) {
        state = state.copyWith(
          errorMessage: e.toString(),
          errorCode: 'PRD_BATCH_UPDATE_ERROR',
          errorSeverity: 'high',
        );
      }
      return [];
    } finally {
      LoggerUtils.methodEnd('batchUpdateProducts', tag: 'ProductNotifier');
    }
  }

  /// 배치 상품 Firebase 동기화 (백그라운드)
  void _syncProductsBatchInBackground(List<Product> products) {
    if (products.isEmpty) return;

    // 로컬 전용 모드: 서버 동기화 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: 상품 배치 서버 동기화 건너뜀 - ${products.length}개', tag: 'ProductNotifier');
  }



  /// 상품명 변경 시 연관된 판매 기록들을 Firebase에 동기화
  Future<void> _syncRelatedSalesLogsToFirebase(int productId, String newProductName) async {
    try {
      LoggerUtils.logInfo('상품명 변경으로 인한 연관 판매 기록 Firebase 동기화 시작: $newProductName', tag: 'ProductNotifier');

      // 해당 상품의 모든 판매 기록 조회 (SalesLogNotifier 사용 안 함)
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      final relatedSalesLogs = await salesLogRepository.getSalesLogsByProductId(productId);

      if (relatedSalesLogs.isNotEmpty) {
        // 로컬 전용 모드: 서버 동기화 제거됨
        LoggerUtils.logInfo('로컬 전용 모드: 연관 판매 기록 서버 동기화 건너뜀 - ${relatedSalesLogs.length}개', tag: 'ProductNotifier');
      }
    } catch (e) {
      LoggerUtils.logError('연관 판매 기록 Firebase 동기화 실패', tag: 'ProductNotifier', error: e);
      // 실패해도 상품 업데이트는 성공했으므로 예외를 던지지 않음
    }
  }

  /// 상품과 연관된 판매 기록을 함께 삭제 (SalesLogNotifier 사용 안 함)
  Future<void> deleteProductWithSalesLogs(Product product, List<SalesLog> salesLogsToDelete) async {
    LoggerUtils.methodStart('deleteProductWithSalesLogs', tag: 'ProductNotifier');
    try {
      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 워크스페이스가 선택되지 않았습니다.');
      }

      // 상품의 eventId와 현재 워크스페이스 ID 일치 확인
      if (product.eventId != currentWorkspace.id) {
        LoggerUtils.logWarning(
          '상품의 eventId(${product.eventId})와 현재 워크스페이스 ID(${currentWorkspace.id})가 일치하지 않습니다. 현재 워크스페이스 기준으로 삭제를 진행합니다.',
          tag: 'ProductNotifier'
        );
        product = product.copyWith(eventId: currentWorkspace.id);
      }

      LoggerUtils.logInfo('상품과 판매 기록 함께 삭제 시작: ${product.name} (ID: ${product.id}, 판매기록: ${salesLogsToDelete.length}개)', tag: 'ProductNotifier');

      // 직접 Firestore 구독을 사용하지 않으므로 무한 루프 방지 캐시 불필요

      // 1. 연관된 판매 기록들을 먼저 삭제 (SalesLogRepository 직접 사용)
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      // 로컬 전용 모드: 동기화 서비스 불필요

      for (final salesLog in salesLogsToDelete) {
        try {
          // 로컬 DB에서 판매 기록 삭제
          await salesLogRepository.deleteSalesLog(salesLog.id);
          LoggerUtils.logInfo('판매 기록 로컬 삭제 완료: ID ${salesLog.id}', tag: 'ProductNotifier');

          // 로컬 전용 모드: Firebase 삭제 불필요
          LoggerUtils.logInfo('로컬 전용 모드: 판매 기록 서버 삭제 건너뜀: ID ${salesLog.id}', tag: 'ProductNotifier');
        } catch (e) {
          LoggerUtils.logError('판매 기록 삭제 실패: ID ${salesLog.id}', tag: 'ProductNotifier', error: e);
          // 개별 판매 기록 삭제 실패해도 계속 진행
        }
      }

      // 2. 상품 삭제
      await repository.deleteProduct(product);
      LoggerUtils.logInfo('상품 로컬 DB 삭제 완료: ${product.name}', tag: 'ProductNotifier');

      // 로컬 전용 모드: Firebase 삭제 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 상품 서버 삭제 건너뜀: ${product.name}', tag: 'ProductNotifier');

      // 4. 상태 업데이트
      if (!mounted) return;
      await loadProducts(showLoading: false);

      LoggerUtils.logInfo('상품과 판매 기록 함께 삭제 완료: ${product.name}', tag: 'ProductNotifier');
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_DELETE_WITH_SALES_ERROR',
        errorSeverity: 'high',
      );
      LoggerUtils.logError('상품과 판매 기록 함께 삭제 실패: ${product.name}', tag: 'ProductNotifier', error: e);
      rethrow;
    }
    LoggerUtils.methodEnd('deleteProductWithSalesLogs', tag: 'ProductNotifier');
  }

  /// 상품과 연관된 판매 기록 조회 (SalesLogNotifier 사용 안 함)
  Future<List<SalesLog>> getRelatedSalesLogs(int productId) async {
    try {
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      return await salesLogRepository.getSalesLogsByProductId(productId);
    } catch (e) {
      LoggerUtils.logError('연관된 판매 기록 조회 실패: productId $productId', tag: 'ProductNotifier', error: e);
      return [];
    }
  }

  /// 상품 삭제 (로컬 DB + Firebase 동기화 + 연관 데이터 처리 포함)
  Future<void> deleteProduct(Product product) async {
    LoggerUtils.methodStart('deleteProduct', tag: 'ProductNotifier');
    try {
      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 워크스페이스가 선택되지 않았습니다.');
      }

      // 상품의 eventId와 현재 워크스페이스 ID 일치 확인
      if (product.eventId != currentWorkspace.id) {
        LoggerUtils.logWarning(
          '상품의 eventId(${product.eventId})와 현재 워크스페이스 ID(${currentWorkspace.id})가 일치하지 않습니다. 현재 워크스페이스 기준으로 삭제를 진행합니다.',
          tag: 'ProductNotifier'
        );
        // 현재 워크스페이스 ID로 업데이트된 상품 객체 생성
        product = product.copyWith(eventId: currentWorkspace.id);
      }

      LoggerUtils.logInfo('상품 삭제 시작: ${product.name} (ID: ${product.id}, EventID: ${product.eventId})', tag: 'ProductNotifier');

      // 직접 Firestore 구독을 사용하지 않으므로 무한 루프 방지 캐시 불필요

      // 1. 연관된 판매 기록 확인 및 안전 처리 (SalesLogNotifier 사용 안 함)
      try {
        final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
        final relatedSalesLogs = await salesLogRepository.getSalesLogsByProductId(product.id!);

        if (relatedSalesLogs.isNotEmpty) {
          LoggerUtils.logInfo('상품 삭제 전 연관된 판매 기록 ${relatedSalesLogs.length}개 확인됨: ${product.name}', tag: 'ProductNotifier');
          // 판매 기록은 유지하되, 상품이 삭제되었음을 로그로만 기록
          // 실제 판매 기록 삭제는 사용자가 명시적으로 요청할 때만 수행
        }
      } catch (e) {
        LoggerUtils.logWarning('연관된 판매 기록 확인 실패 (삭제는 계속 진행): ${product.name}', tag: 'ProductNotifier', error: e);
      }

      // 2. 로컬 DB에서 삭제
      await repository.deleteProduct(product);
      LoggerUtils.logInfo('상품 로컬 DB 삭제 완료: ${product.name}', tag: 'ProductNotifier');

      // 2-1. 선입금 가상상품 연동 링크 정리 (이 상품과 연결된 링크 제거)
      try {
        final linkNotifier = ref.read(prepaymentProductLinkNotifierProvider.notifier);
        await linkNotifier.removeLinksByProductIds([product.id!]);
        LoggerUtils.logInfo('상품 관련 선입금 연동 제거 완료: ${product.name}', tag: 'ProductNotifier');
      } catch (e) {
        LoggerUtils.logError('상품 관련 선입금 연동 제거 실패: ${product.name}', tag: 'ProductNotifier', error: e);
      }

      // 로컬 전용 모드: Firebase 삭제 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 상품 서버 삭제 건너뜀: ${product.name}', tag: 'ProductNotifier');

      // 4. 관련 세트 할인 삭제
      LoggerUtils.logInfo('상품 삭제 - 관련 세트 할인 삭제 시작: ${product.name} (ID: ${product.id})', tag: 'ProductNotifier');
      await _deleteRelatedSetDiscounts(product);
      LoggerUtils.logInfo('상품 삭제 - 관련 세트 할인 삭제 완료: ${product.name}', tag: 'ProductNotifier');

      // 5. 관련 Provider들에게 상품 삭제 알림
      _notifyRelatedProviders(product);

      // 6. 상태 업데이트
      if (!mounted) return;
      await loadProducts(showLoading: false);
      LoggerUtils.logInfo('상품 삭제 완료: ${product.name}', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('상품 삭제 실패: ${product.name}', tag: 'ProductNotifier', error: e);

      // 직접 Firestore 구독을 사용하지 않으므로 캐시 관리 불필요

      if (!mounted) return;
      state = state.copyWith(
        errorMessage: '상품 삭제 중 오류가 발생했습니다: ${e.toString()}',
        errorCode: 'PRD_DELETE_ERROR',
        errorSeverity: 'high',
      );
    }
    LoggerUtils.methodEnd('deleteProduct', tag: 'ProductNotifier');
  }

  /// 여러 상품을 배치로 삭제 (로컬 DB + Firebase 배치 처리)
  Future<Map<String, dynamic>> deleteBatchProducts(List<Product> products) async {
    LoggerUtils.methodStart('deleteBatchProducts', tag: 'ProductNotifier');

    final results = <String, dynamic>{
      'success': 0,
      'failed': 0,
      'errors': <String>[],
    };

    try {
      if (products.isEmpty) return results;

      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 워크스페이스가 선택되지 않았습니다.');
      }

      LoggerUtils.logInfo('상품 배치 삭제 시작: ${products.length}개', tag: 'ProductNotifier');

      // 1. 로컬 DB에서 병렬 삭제
      final localDeleteResults = await Future.wait(
        products.map((product) async {
          try {
            // 직접 Firestore 구독을 사용하지 않으므로 캐시 관리 불필요

            await repository.deleteProduct(product);
            LoggerUtils.logInfo('로컬 삭제 성공: ${product.name}', tag: 'ProductNotifier');
            return {'success': true, 'product': product};
          } catch (e) {
            LoggerUtils.logError('로컬 삭제 실패: ${product.name}', error: e, tag: 'ProductNotifier');
            return {'success': false, 'product': product, 'error': e.toString()};
          }
        }),
        eagerError: false,
      );

      // 로컬 삭제 성공한 상품들만 Firebase에서 삭제
      final localSuccessProducts = localDeleteResults
          .where((result) => result['success'] == true)
          .map((result) => result['product'] as Product)
          .toList();

      results['success'] = localSuccessProducts.length;
      results['failed'] = products.length - localSuccessProducts.length;

      // 로컬 전용 모드: Firebase 배치 삭제 불필요
      if (localSuccessProducts.isNotEmpty) {
        LoggerUtils.logInfo('로컬 전용 모드: 상품 배치 서버 삭제 건너뜀: ${localSuccessProducts.length}개', tag: 'ProductNotifier');
      }

      // 2-1. 성공적으로 삭제된 상품들에 대해 관련 세트 할인 삭제
      for (final product in localSuccessProducts) {
        try {
          LoggerUtils.logInfo('상품 배치 삭제 - 관련 세트 할인 삭제 시작: ${product.name} (ID: ${product.id})', tag: 'ProductNotifier');
          await _deleteRelatedSetDiscounts(product);
          LoggerUtils.logInfo('상품 배치 삭제 - 관련 세트 할인 삭제 완료: ${product.name}', tag: 'ProductNotifier');
        } catch (e) {
          LoggerUtils.logError('상품 배치 삭제 - 관련 세트 할인 삭제 실패: ${product.name}', error: e, tag: 'ProductNotifier');
        }
      }

      // 3. 상태 업데이트
      if (!mounted) return results;
      await loadProducts(showLoading: false);

      LoggerUtils.logInfo('상품 배치 삭제 완료: 성공 ${results['success']}개, 실패 ${results['failed']}개', tag: 'ProductNotifier');

    } catch (e) {
      LoggerUtils.logError('상품 배치 삭제 실패', error: e, tag: 'ProductNotifier');

      if (!mounted) return results;
      state = state.copyWith(
        errorMessage: '상품 배치 삭제 중 오류가 발생했습니다: ${e.toString()}',
        errorCode: 'PRD_BATCH_DELETE_ERROR',
        errorSeverity: 'high',
      );

      results['failed'] = products.length;
      results['success'] = 0;
      results['errors'].add(e.toString());
    }

    LoggerUtils.methodEnd('deleteBatchProducts', tag: 'ProductNotifier');
    return results;
  }

  /// 판매자 이름 변경 시 해당 판매자의 모든 상품 업데이트
  Future<void> updateSellerNameForAllProducts(String oldName, String newName, int eventId) async {
    try {
      LoggerUtils.logInfo('판매자 이름 변경으로 인한 상품 일괄 업데이트 시작: $oldName → $newName', tag: 'ProductNotifier');

      // 해당 판매자의 모든 상품 조회 (데이터베이스에서 직접)
      final allProducts = await repository.getProductsByEventId(eventId);
      final productsToUpdate = allProducts.where((product) => product.sellerName == oldName).toList();

      if (productsToUpdate.isEmpty) {
        LoggerUtils.logInfo('업데이트할 상품이 없습니다: $oldName', tag: 'ProductNotifier');
        return;
      }

      LoggerUtils.logInfo('업데이트할 상품 수: ${productsToUpdate.length}개', tag: 'ProductNotifier');

      // 트랜잭션으로 일괄 업데이트 (원자성 보장)
      final db = await ref.read(databaseServiceProvider).database;
      await db.transaction((txn) async {
        for (final product in productsToUpdate) {
          await txn.update(
            'products',
            {'sellerName': newName},
            where: 'id = ?',
            whereArgs: [product.id],
          );
        }
      });

      // 상태 강제 갱신 (UI 즉시 반영)
      if (mounted) {
        await loadProducts(showLoading: false);
        // 추가 지연으로 UI 갱신 보장
        await Future.delayed(const Duration(milliseconds: 100));
      }

      LoggerUtils.logInfo('판매자 이름 변경으로 인한 상품 일괄 업데이트 완료: ${productsToUpdate.length}개', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('판매자 이름 변경으로 인한 상품 일괄 업데이트 실패', tag: 'ProductNotifier', error: e);
      rethrow;
    }
  }

  /// 관련 Provider들에게 상품 업데이트 알림
  void _notifyRelatedProviders(Product updatedProduct) {
    try {
      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 판매 기록 Provider 갱신 (상품명 변경 시 필요)
          ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

          // 선입금 Provider 갱신 (상품과 연관된 선입금이 있는 경우)
          ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

          LoggerUtils.logInfo('상품 관련 Provider들 갱신 완료: ${updatedProduct.name}', tag: 'ProductNotifier');
        } catch (e) {
          LoggerUtils.logError('상품 관련 Provider 갱신 실패', tag: 'ProductNotifier', error: e);
        }
      });
    } catch (e) {
      LoggerUtils.logError('상품 관련 Provider 알림 실패', tag: 'ProductNotifier', error: e);
    }
  }

  // UI에서 필요한 추가 메서드들

  Future<Product?> getProductById(int id) async {
    try {
      return await repository.getProductById(id);
    } catch (e) {
      LoggerUtils.logError('Error getting product by id: $e', tag: 'ProductNotifier');
      return null;
    }
  }

  Future<void> updateProductStock(int productId, int newStock) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final updatedProduct = product.copyWith(quantity: newStock);
        await updateProduct(updatedProduct);
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_UPDATE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  Future<void> increaseStock(int productId, int amount) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final newStock = product.quantity + amount;
        await updateProductStock(productId, newStock);
      } else {
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '해당 상품이 이미 삭제되어 재고를 복원할 수 없습니다.',
          errorCode: 'PRD_NOT_FOUND_ERROR',
          errorSeverity: 'high',
        );
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_INCREASE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  Future<void> decreaseStock(int productId, int amount) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final newStock = product.quantity - amount;
        if (newStock >= 0) {
          await updateProductStock(productId, newStock);
        } else {
          if (!mounted) return;
          state = state.copyWith(
            errorMessage: '재고가 부족합니다. 현재 재고: ${product.quantity}, 요청 수량: $amount',
            errorCode: 'PRD_INSUFFICIENT_STOCK_ERROR',
            errorSeverity: 'high',
          );
        }
      } else {
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '해당 상품이 이미 삭제되어 재고를 차감할 수 없습니다.',
          errorCode: 'PRD_NOT_FOUND_ERROR',
          errorSeverity: 'high',
        );
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_DECREASE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  // ====== 정렬/필터/검색 로직 완전 새 구현 + 안정성 큐잉 추가 ======

  bool _isProcessing = false;
  VoidCallback? _pendingRequest;

  void setSortOption(ProductSortOption sortOption) {
    if (!mounted) return;
    final allowed = [
      ProductSortOption.recentlyAdded,
      ProductSortOption.nameAsc, ProductSortOption.nameDesc,
      ProductSortOption.priceAsc, ProductSortOption.priceDesc,
      ProductSortOption.quantityAsc, ProductSortOption.quantityDesc,
    ];
    final next = allowed.contains(sortOption) ? sortOption : ProductSortOption.recentlyAdded;
    state = state.copyWith(currentSortOption: next);
    _saveSettings(); // 설정 저장
    _requestApplySortAndFilters();
  }

  void setSellerFilter(String sellerName) {
    if (!mounted) return;
    state = state.copyWith(selectedSellerFilter: sellerName);
    _saveSettings(); // 설정 저장
    _requestApplySortAndFilters();
  }

  void searchProducts(String query) async {
    if (!mounted) return;
    _searchQuery = query;
    _debounceTimer?.cancel();

    // Timer 대신 Future.delayed 사용 (안전한 비동기 처리)
    await Future.delayed(const Duration(milliseconds: 300));
    if (!mounted) return;
    _saveSettings(); // 설정 저장
    _requestApplySortAndFilters();
  }

  void _requestApplySortAndFilters() {
    if (_isProcessing) {
      _pendingRequest = _applySortAndFilters;
      return;
    }
    _applySortAndFilters();
  }

  void _applySortAndFilters() {
    if (!mounted) return;
    _isProcessing = true;
    List<Product> filtered = List.from(state.products);

    // 판매자 필터 적용
    if (state.selectedSellerFilter.isNotEmpty) {
      filtered = filtered.where((product) => product.sellerName == state.selectedSellerFilter).toList();
    }

    // 검색 필터 적용
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((product) =>
        product.name.toLowerCase().contains(query) ||
        (product.sellerName?.toLowerCase().contains(query) ?? false)
      ).toList();
    }

    // 정렬 적용 (품절 상품을 뒤로 밀기 + 기존 정렬 로직)
    filtered.sort((a, b) {
      // 품절 상품을 항상 뒤로 밀기 (재고가 0인 상품)
      final aOutOfStock = a.quantity <= 0;
      final bOutOfStock = b.quantity <= 0;

      if (aOutOfStock && !bOutOfStock) {
        return 1; // a가 품절이면 뒤로
      } else if (!aOutOfStock && bOutOfStock) {
        return -1; // b가 품절이면 a가 앞으로
      }

      // 둘 다 품절이거나 둘 다 재고가 있는 경우 기존 정렬 로직 적용
      switch (state.currentSortOption) {
        case ProductSortOption.nameAsc:
          return a.name.compareTo(b.name);
        case ProductSortOption.nameDesc:
          return b.name.compareTo(a.name);
        case ProductSortOption.priceAsc:
          return a.price.compareTo(b.price);
        case ProductSortOption.priceDesc:
          return b.price.compareTo(a.price);
        case ProductSortOption.quantityAsc:
          return a.quantity.compareTo(b.quantity);
        case ProductSortOption.quantityDesc:
          return b.quantity.compareTo(a.quantity);
        case ProductSortOption.recentlyAdded:
        default:
          return (b.id ?? 0).compareTo(a.id ?? 0);
      }
    });

    state = state.copyWith(filteredProducts: filtered);
    _isProcessing = false;
    if (_pendingRequest != null) {
      final pending = _pendingRequest;
      _pendingRequest = null;
      pending?.call();
    }
  }
  // ====== 정렬/필터/검색 로직 완전 새 구현 + 안정성 큐잉 끝 ======

  void clearError() {
    if (!mounted) return;
    state = state.copyWith(
      errorMessage: null,
      errorCode: null,
      errorSeverity: null,
    );
  }

  // ===== 설정 저장/복원 기능 =====

  /// 현재 정렬/필터 설정을 저장합니다
  Future<void> _saveSettings() async {
    try {
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 정렬 옵션 저장
      await settingsRepository.setPageSortOption(_pageKey, state.currentSortOption.value);

      // 필터 설정 저장 (JSON 형태)
      final filterSettings = {
        'selectedSellerFilter': state.selectedSellerFilter,
        'searchQuery': _searchQuery,
      };
      await settingsRepository.setPageFilterSettings(_pageKey, jsonEncode(filterSettings));

      LoggerUtils.logDebug('상품 관리 설정 저장 완료', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('상품 관리 설정 저장 실패', tag: 'ProductNotifier', error: e);
    }
  }

  /// 저장된 정렬/필터 설정을 복원합니다
  Future<void> _restoreSettings() async {
    try {
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 정렬 옵션 복원
      final savedSortOption = await settingsRepository.getPageSortOption(_pageKey);
      if (savedSortOption != null) {
        final sortOption = ProductSortOption.values.firstWhere(
          (option) => option.value == savedSortOption,
          orElse: () => ProductSortOption.recentlyAdded,
        );
        state = state.copyWith(currentSortOption: sortOption);
      }

      // 필터 설정 복원
      final savedFilterSettings = await settingsRepository.getPageFilterSettings(_pageKey);
      if (savedFilterSettings != null) {
        try {
          final filterMap = jsonDecode(savedFilterSettings) as Map<String, dynamic>;
          final sellerFilter = filterMap['selectedSellerFilter'] as String? ?? '';
          final searchQuery = filterMap['searchQuery'] as String? ?? '';

          state = state.copyWith(selectedSellerFilter: sellerFilter);
          _searchQuery = searchQuery;
        } catch (e) {
          LoggerUtils.logError('필터 설정 복원 중 JSON 파싱 오류', tag: 'ProductNotifier', error: e);
        }
      }

      LoggerUtils.logDebug('상품 관리 설정 복원 완료', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('상품 관리 설정 복원 실패', tag: 'ProductNotifier', error: e);
    }
  }

  /// 상품 삭제 시 관련 세트 할인 삭제
  Future<void> _deleteRelatedSetDiscounts(Product product) async {
    try {
      LoggerUtils.logInfo('_deleteRelatedSetDiscounts 시작 - 상품: ${product.name} (ID: ${product.id})', tag: 'ProductNotifier');

      if (product.id == null) {
        LoggerUtils.logWarning('상품 ID가 null이므로 세트 할인 삭제 건너뜀', tag: 'ProductNotifier');
        return;
      }

      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 null이므로 세트 할인 삭제 건너뜀', tag: 'ProductNotifier');
        return;
      }

      LoggerUtils.logInfo('현재 워크스페이스: ${currentWorkspace.id}, 상품 ID: ${product.id}', tag: 'ProductNotifier');

      final setDiscountRepository = SetDiscountRepository(database: ref.read(databaseServiceProvider));
      final relatedSetDiscounts = await setDiscountRepository.getSetDiscountsByProductId(
        product.id!,
        eventId: currentWorkspace.id,
      );

      LoggerUtils.logInfo('상품 ID ${product.id}와 관련된 세트 할인 ${relatedSetDiscounts.length}개 발견', tag: 'ProductNotifier');

      if (relatedSetDiscounts.isNotEmpty) {
        for (final setDiscount in relatedSetDiscounts) {
          LoggerUtils.logInfo('관련 세트 할인 발견: ${setDiscount.name} (ID: ${setDiscount.id}, 타입: ${setDiscount.conditionType})', tag: 'ProductNotifier');
          LoggerUtils.logInfo('세트 할인 상품 IDs: ${setDiscount.productIds}', tag: 'ProductNotifier');
        }

        final setDiscountNotifier = ref.read(setDiscountNotifierProvider.notifier);
        int deletedCount = 0;

        for (final setDiscount in relatedSetDiscounts) {
          if (setDiscount.id != null) {
            LoggerUtils.logInfo('세트 할인 삭제 시도: ${setDiscount.name} (ID: ${setDiscount.id})', tag: 'ProductNotifier');
            final success = await setDiscountNotifier.deleteSetDiscount(setDiscount.id!);
            if (success) {
              deletedCount++;
              LoggerUtils.logInfo('세트 할인 삭제 성공: ${setDiscount.name}', tag: 'ProductNotifier');
            } else {
              LoggerUtils.logError('세트 할인 삭제 실패: ${setDiscount.name}', tag: 'ProductNotifier');
            }
          } else {
            LoggerUtils.logWarning('세트 할인 ID가 null이므로 삭제 건너뜀: ${setDiscount.name}', tag: 'ProductNotifier');
          }
        }

        if (deletedCount > 0) {
          LoggerUtils.logInfo('상품 삭제로 인해 ${deletedCount}개의 세트 할인이 삭제되었습니다.', tag: 'ProductNotifier');
        } else {
          LoggerUtils.logWarning('관련 세트 할인이 있었지만 삭제된 것이 없습니다.', tag: 'ProductNotifier');
        }
      } else {
        LoggerUtils.logInfo('상품 ID ${product.id}와 관련된 세트 할인이 없습니다.', tag: 'ProductNotifier');
      }
    } catch (e) {
      LoggerUtils.logError('관련 세트 할인 삭제 실패', error: e, tag: 'ProductNotifier');
    }
  }



  @override
  void dispose() {
    // Timer 제거로 메모리 누수 방지
    _debounceTimer?.cancel();
    // 로컬 전용 모드: 실시간 동기화 구독 해제 제거됨
    // 직접 Firestore 구독은 더 이상 사용하지 않음
    super.dispose();
  }
}
