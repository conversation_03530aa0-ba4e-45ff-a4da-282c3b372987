import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// 로컬 전용 모드: Firebase 관련 import 제거됨
import 'dart:async';

import '../models/sales_log.dart';
import '../models/sales_log_display_item.dart';
import '../models/sales_stat_item.dart';
import '../models/transaction_type.dart';
import '../models/event_workspace.dart';
// 로컬 전용 모드: subscription_plan import 제거됨
import '../repositories/product_repository.dart';
import '../repositories/sales_log_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/state_sync_manager.dart';
import '../providers/settings_provider.dart';
import 'dart:convert';
// 로컬 전용 모드: 네트워크 상태 관련 import 제거됨
import '../services/database_service.dart';
// 로컬 전용 모드: subscription_provider import 제거됨
import '../mixins/workspace_aware_provider_mixin.dart';

import 'sales_log/sales_log_batch_operations.dart';
import 'sales_log/sales_log_filter_sort.dart';
import 'sales_log/sales_log_stats.dart';
import 'sales_log/sales_log_crud.dart';
import 'sales_log/sales_log_state.dart';
import 'unified_workspace_provider.dart';
import '../utils/event_workspace_utils.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨

import 'product_provider.dart';

/// 판매 기록(영수증/로그) 상태를 관리하는 State 클래스입니다.
/// - 전체 판매 기록, 필터링/정렬 결과, 검색어, 로딩/에러/업데이트 상태 등 포함
/// - ProviderException, isCancelled 등 오류/취소/비동기 상태도 함께 관리

/// 상품 데이터베이스 접근을 위한 Repository Provider입니다.
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return ProductRepository(database: databaseService);
});

/// 판매 기록 데이터베이스 접근을 위한 Repository Provider입니다.
/// - DB 서비스와 연동하여 SalesLogRepository를 생성합니다.
final salesLogRepositoryProvider = Provider<SalesLogRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SalesLogRepository(database: databaseService);
});

/// 판매 기록 상태를 관리하는 Provider입니다.
class SalesLogNotifier extends StateNotifier<SalesLogState> with WorkspaceAwareProviderMixin<SalesLogState> {
  static const String _tag = 'SalesLogNotifier';
  static const String _pageKey = 'sales_log';

  @override
  final Ref ref;

  @override
  String get providerTag => 'SalesLogNotifier';

  // 로컬 전용 모드: 다운로드 관련 플래그 제거됨
  late SalesLogBatchOperations _batchOperations;
  late SalesLogFilterSort _filterSort;
  late SalesLogStats _stats;
  late SalesLogCrud _crud;
  // 로컬 전용 모드: 실시간 동기화 제거됨

  // 무한 루프 방지를 위한 최근 추가한 판매 기록 캐시
  final Set<int> _recentlyAddedSalesLogs = <int>{};

  // 순환 호출 방지를 위한 플래그
  bool _isNotifyingRelatedProviders = false;

  SalesLogNotifier(this.ref) : super(const SalesLogState()) {
    _initializeComponents();
    _initialize();
  }

  Future<void> _initialize() async {
    await _restoreSettings();
    await loadSalesLogs();
    watchCurrentEvent(); // Mixin의 메서드 사용
    _setupRealtimeSync();
  }

  /// Mixin에서 요구하는 워크스페이스 변경 처리 메서드
  @override
  Future<void> onWorkspaceChanged(EventWorkspace? workspace) async {
    if (workspace != null) {
      await loadSalesLogs();
      _setupRealtimeSync(); // 실시간 동기화 활성화
    } else {
      // 현재 행사 워크스페이스가 null이 되면 판매 기록 목록 클리어
      state = state.copyWith(
        salesLogs: [],
        errorMessage: getNoWorkspaceErrorMessage(),
      );
    }
  }

  /// Mixin에서 요구하는 메모리 정리 메서드
  @override
  void clearDataForEventTransition() {
    _clearAllDataForEventTransition();
  }

  /// Mixin에서 요구하는 실시간 동기화 설정 메서드
  @override
  void setupRealtimeSync() {
    _setupRealtimeSync();
  }

  /// 로컬 전용 모드: 실시간 동기화 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드에서는 실시간 동기화를 사용하지 않음
  }

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  // 로컬 전용 모드: 사용하지 않는 메서드 제거됨

  // 로컬 전용 모드: 사용하지 않는 메서드 제거됨



  // 로컬 전용 모드: 사용하지 않는 메서드 완전 삭제

  void _initializeComponents() {
    final repository = ref.read(salesLogRepositoryProvider);
    final productRepository = ref.read(productRepositoryProvider);
    _batchOperations = SalesLogBatchOperations(
      salesLogRepository: repository,
      updateState: (state) => updateState((current) => state),
    );
    _filterSort = SalesLogFilterSort(
      updateState: (state) => updateState((current) => state),
      createDisplayItems: _createDisplayItems,
    );
    _stats = SalesLogStats(
      salesLogRepository: repository,
      updateState: (state) => updateState((current) => state),
      ref: ref,
    );
    _crud = SalesLogCrud(
      salesLogRepository: repository,
      productRepository: productRepository,
      updateState: (state) => updateState((current) => state),
      refreshState: _refreshState,
      // 로컬 전용 모드: ref 파라미터 제거
    );
  }

  /// 페이지네이션을 사용한 판매 기록 로드 (POS 최적화)
  Future<void> loadSalesLogsPaginated({
    bool reset = false,
    String? searchQuery,
    String? sellerFilter,
  }) async {
    if (!mounted) return;

    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: _tag);
      if (!mounted) return;
      state = state.copyWith(
        salesLogs: [],
        isLoading: false,
        errorMessage: '행사 워크스페이스를 선택해주세요.',
      );
      return;
    }

    // 리셋이면 첫 페이지부터 시작
    final currentPage = reset ? 0 : state.currentPage;
    final isFirstLoad = reset || state.salesLogs.isEmpty;

    if (isFirstLoad) {
      state = state.copyWith(isLoading: true, currentPage: 0);
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final repository = ref.read(salesLogRepositoryProvider);

      // 페이지네이션으로 판매 기록 조회
      final newSalesLogs = await repository.getSalesLogsByEventPaginated(
        eventId: currentWorkspace.id,
        page: currentPage,
        pageSize: state.pageSize,
        searchQuery: searchQuery,
        sellerFilter: sellerFilter,
      );

      // 총 개수 조회 (첫 로드시에만)
      int totalCount = state.totalCount;
      if (isFirstLoad) {
        totalCount = await repository.getSalesLogsCount(
          eventId: currentWorkspace.id,
          searchQuery: searchQuery,
          sellerFilter: sellerFilter,
        );
      }

      // 기존 데이터와 병합 또는 교체
      final updatedSalesLogs = isFirstLoad
          ? newSalesLogs
          : [...state.salesLogs, ...newSalesLogs];

      final hasMoreData = newSalesLogs.length == state.pageSize;

      if (!mounted) return;
      state = state.copyWith(
        salesLogs: updatedSalesLogs,
        currentPage: currentPage + 1,
        totalCount: totalCount,
        hasMoreData: hasMoreData,
        isLoading: false,
        isLoadingMore: false,
        errorMessage: null,
      );

      LoggerUtils.logInfo('페이지네이션 판매 기록 로드 완료: ${newSalesLogs.length}개 (총 ${updatedSalesLogs.length}개)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('페이지네이션 판매 기록 로드 실패', tag: _tag, error: e);
      if (!mounted) return;
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 클라이언트 사이드 페이지네이션 - 특정 페이지로 이동
  void goToPage(int page) {
    if (page < 1) return;

    // 필터링된 데이터가 있으면 그것을 기준으로, 없으면 전체 데이터 기준으로 페이지 계산
    final sourceData = state.filteredSalesLogs.isNotEmpty
        ? state.filteredSalesLogs
        : state.allSalesLogs;
    final totalPages = (sourceData.length / state.pageSize).ceil();
    if (page > totalPages && totalPages > 0) return;

    state = state.copyWith(currentPage: page);
    _updateDisplayedSalesLogs();
  }

  /// 다음 페이지로 이동
  void nextPage() {
    final sourceData = state.filteredSalesLogs.isNotEmpty
        ? state.filteredSalesLogs
        : state.allSalesLogs;
    final totalPages = (sourceData.length / state.pageSize).ceil();
    if (state.currentPage < totalPages) {
      goToPage(state.currentPage + 1);
    }
  }

  /// 이전 페이지로 이동
  void previousPage() {
    if (state.currentPage > 1) {
      goToPage(state.currentPage - 1);
    }
  }

  /// 페이지 크기 변경
  void changePageSize(int newPageSize) {
    if (newPageSize == state.pageSize) return;

    state = state.copyWith(
      pageSize: newPageSize,
      currentPage: 1, // 첫 페이지로 리셋
    );
    _updateDisplayedSalesLogs();
  }

  /// 현재 페이지에 표시할 데이터 업데이트
  void _updateDisplayedSalesLogs() {
    // 필터링된 데이터가 있으면 그것을 사용, 없으면 전체 데이터 사용
    final sourceData = state.filteredSalesLogs.isNotEmpty
        ? state.filteredSalesLogs
        : state.allSalesLogs;

    // 먼저 전체 데이터로 displayItems 생성
    final allDisplayItems = _createDisplayItems(sourceData);

    // displayItems 기준으로 페이징
    final startIndex = (state.currentPage - 1) * state.pageSize;
    final endIndex = (startIndex + state.pageSize).clamp(0, allDisplayItems.length);

    final displayedItems = allDisplayItems.sublist(startIndex, endIndex);

    // displayItems에서 실제 SalesLog 추출 (UI 호환성을 위해)
    final displayedLogs = <SalesLog>[];
    for (final item in displayedItems) {
      if (item is SingleItem) {
        displayedLogs.add(item.salesLog);
      } else if (item is GroupedSale) {
        displayedLogs.addAll(item.items);
      }
    }

    LoggerUtils.logDebug(
      '페이징 업데이트: 전체=${state.allSalesLogs.length}, 필터링=${state.filteredSalesLogs.length}, 소스=${sourceData.length}, 전체displayItems=${allDisplayItems.length}, 페이지=${state.currentPage}, 크기=${state.pageSize}, 시작=$startIndex, 끝=$endIndex, 표시displayItems=${displayedItems.length}',
      tag: _tag,
    );

    state = state.copyWith(
      salesLogs: displayedLogs,
      displayItems: displayedItems,
      totalCount: allDisplayItems.length, // displayItems 기준으로 총 개수 설정
      hasMoreData: state.currentPage < (allDisplayItems.length / state.pageSize).ceil(),
    );
  }

  /// 기존 loadSalesLogs 메서드 (호환성 유지)
  Future<void> loadSalesLogs({bool showLoading = true}) async {
    if (!mounted) return;

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }

    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'SalesLogNotifier');
        if (!mounted) return;
        state = state.copyWith(
          salesLogs: [],
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
        );
        return;
      }

      // 로컬 DB에서 판매기록 로드 (모든 플랜에서 가능)
      final repository = ref.read(salesLogRepositoryProvider);
      final logs = await repository.getSalesLogsByEventId(currentWorkspace.id);
      if (!mounted) return;

      // 전체 데이터를 allSalesLogs에 저장하고, 첫 페이지 데이터만 salesLogs에 저장
      state = state.copyWith(
        allSalesLogs: logs,
        filteredSalesLogs: const [], // 새로 로드 시 필터링 목록 초기화하여 최신 전체 데이터 사용
        currentPage: 1,
        isLoading: false,
        errorMessage: null,
      );

      // 첫 페이지 데이터 표시 (allSalesLogs 기준)
      _updateDisplayedSalesLogs();

      // Firebase에서 최신 데이터 동기화 (프로 플랜에서만, 초기 로딩 시에만, 중복 방지)
      // 로컬 전용 모드: 프로 플랜 체크 및 동기화 관련 로직 제거됨
      // 로컬 전용 모드: 실시간 동기화 관련 코드 제거됨
    } catch (e) {
      LoggerUtils.logError('판매기록 로딩 실패', tag: _tag, error: e);
      if (!mounted) return;
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      // 기존 salesLogs 데이터는 유지
    } finally {
      // 안전장치: 어떤 경우든 로딩 상태를 false로 설정
      if (mounted && state.isLoading) {
        state = state.copyWith(isLoading: false);
      }
    }
  }

  void updateState(SalesLogState Function(SalesLogState) update) {
    if (!mounted) return;
    state = update(state);
  }

  Future<void> _refreshState() async {
    if (!mounted) return;
    await loadSalesLogs();
  }

  List<SalesLogDisplayItem> _createDisplayItems(List<SalesLog> logs) {
    final Map<String?, List<SalesLog>> groupedLogs = {};
    final List<SalesLog> singleLogs = [];

    for (final log in logs) {
      final batchId = log.batchSaleId;
      if (batchId != null && batchId.isNotEmpty) {
        groupedLogs.putIfAbsent(batchId, () => []).add(log);
      } else {
        singleLogs.add(log);
      }
    }

    final List<SalesLogDisplayItem> displayItems = [];

    // 그룹화된 판매 추가
    for (final entry in groupedLogs.entries) {
      displayItems.add(GroupedSale(entry.value, entry.key!));
    }

    // 개별 판매 추가
    for (final log in singleLogs) {
      displayItems.add(SingleItem(log));
    }

    // 타임스탬프 기준 내림차순 정렬
    displayItems.sort(
      (a, b) => b.representativeTimestampMillis().compareTo(
        a.representativeTimestampMillis(),
      ),
    );

    return displayItems;
  }

  // ===== 배치 처리 메서드들 =====
  
  /// 배치 처리 취소
  void cancelBatchOperation() {
    _batchOperations.cancelBatchOperation();
  }

  /// 대용량 판매 기록 일괄 삽입
  Future<void> insertSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.insertSalesLogsBatch(logs);
  }

  /// 대용량 판매 기록 일괄 업데이트
  Future<void> updateSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.updateSalesLogsBatch(logs);
  }

  /// 대용량 판매 기록 일괄 삭제
  Future<void> deleteSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.deleteSalesLogsBatch(logs);
  }

  /// 판매 기록 일괄 처리
  Future<void> processBatchOperation({
    required List<String> logIds,
    required String operation,
  }) async {
    await _batchOperations.processBatchOperation(
      logIds: logIds,
      operation: operation,
    );
  }

  // ===== 검색/필터링/정렬 메서드들 =====

  /// 디바운스된 검색 기능
  void searchSalesLogs(String query) {
    final currentState = state;
    
    _filterSort.searchSalesLogs(query, currentState.salesLogs);
  }

  /// 디바운스된 필터링 기능
  void filterSalesLogs({
    String? sellerName,
    TransactionType? transactionType,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final currentState = state;
    
    _filterSort.filterSalesLogs(
      salesLogs: currentState.salesLogs,
      sellerName: sellerName,
      transactionType: transactionType,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// 디바운스된 정렬 기능
  void sortSalesLogs(String sortBy, bool ascending) {
    final currentState = state;
    
    _filterSort.sortSalesLogs(sortBy, ascending, currentState.salesLogs);
  }

  /// 필터 초기화
  void clearFilters() {
    final currentState = state;
    
    _filterSort.clearFilters(currentState.salesLogs);
  }

  /// 거래 타입 필터 설정
  void setTypeFilter(TransactionType? transactionType) {
    final currentState = state;

    _filterSort.setTypeFilter(transactionType, currentState.salesLogs);
    _saveSettings();
  }

  /// 판매자 필터 설정
  void setSellerFilter(String sellerName) {
    final currentState = state;

    _filterSort.setSellerFilter(sellerName, currentState.salesLogs);
    _saveSettings();
  }

  // ===== 통계 메서드들 =====

  /// 전체 판매 통계 로드
  Future<List<SalesStatItem>> loadOverallSalesStats() async {
    return await _stats.loadOverallSalesStats();
  }

  /// 총 판매 금액 계산
  Future<int> getTotalSalesAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    return await _stats.getTotalSalesAmount(
      startTime: startTime,
      endTime: endTime,
      sellerName: sellerName,
    );
  }

  /// 총 할인 금액 계산
  Future<int> getTotalDiscountAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    return await _stats.getTotalDiscountAmount(
      startTime: startTime,
      endTime: endTime,
      sellerName: sellerName,
    );
  }

  // ===== CRUD 메서드들 =====

  /// 판매 기록 추가
  Future<void> addSalesLog(SalesLog newSalesLog) async {
    if (!mounted) return;

    LoggerUtils.logDebug('판매 로그 추가 시작', tag: _tag);
    try {
      // 현재 선택된 행사 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) {
        LoggerUtils.logWarning('현재 선택된 행사가 없습니다', tag: _tag);
        if (!mounted) return;
        state = state.copyWith(errorMessage: '행사를 선택해주세요');
        return;
      }

      // 판매 기록에 현재 행사 ID 설정
      final salesLogWithEventId = newSalesLog.copyWith(eventId: currentEvent.id!);

      // DB에 저장하고 실제 ID를 가진 SalesLog 반환받기
      final savedSalesLog = await _crud.addSalesLog(salesLogWithEventId);
      LoggerUtils.logDebug('판매 로그 저장 완료: ID ${savedSalesLog.id}', tag: _tag);

      // 최근 추가한 판매 기록으로 캐시 (무한 루프 방지용)
      _recentlyAddedSalesLogs.add(savedSalesLog.id);
      // 5초 후 캐시에서 제거
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedSalesLogs.remove(savedSalesLog.id);
      });

      // 2. Firebase에 즉시 업로드 (실시간 동기화를 위해 - 플랜별 제한)
      try {
        // 플랜별 서버 동기화 기능 확인
        // 로컬 전용 모드: 사용하지 않는 변수들 제거됨

        // 로컬 전용 모드: 서버 동기화 제거됨
      } catch (e) {
        LoggerUtils.logError('판매 기록 Firebase 업로드 실패 (로컬 저장은 성공): ${savedSalesLog.productName}', tag: _tag, error: e);
        // Firebase 업로드 실패해도 로컬 저장은 성공했으므로 계속 진행
      }

      // 효율적인 상태 갱신: 실제 DB ID를 가진 로그를 현재 상태에 추가
      LoggerUtils.logDebug('판매 로그 상태 갱신 시작', tag: _tag);
      final currentLogs = List<SalesLog>.from(state.salesLogs);
      currentLogs.add(savedSalesLog);

      final newDisplayItems = _createDisplayItems(currentLogs);

      if (!mounted) return;
      state = state.copyWith(
        salesLogs: currentLogs,
        filteredSalesLogs: currentLogs,
        allSalesLogs: [savedSalesLog, ...state.allSalesLogs],
        displayItems: newDisplayItems,
      );
      LoggerUtils.logDebug('판매 로그 상태 갱신 완료', tag: _tag);
      
    } catch (e) {
      LoggerUtils.logError('판매 로그 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 수정
  Future<void> updateSalesLog(SalesLog salesLog) async {
    if (!mounted) return;

    try {
      await _crud.updateSalesLog(salesLog);

      // 로컬 전용 모드: 실시간 동기화 불필요

      // 관련 Provider들에게 판매 기록 업데이트 알림
      _notifyRelatedProviders(salesLog);
    } catch (e) {
      LoggerUtils.logError('판매 기록 수정 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updateSalesLogFields(int salesLogId, Map<String, dynamic> fields) async {
    if (!mounted) return;

    try {
      LoggerUtils.methodStart('updateSalesLogFields', tag: _tag, data: {'id': salesLogId, 'fields': fields.keys.toList()});

      // 현재 판매 기록 조회
      final currentSalesLog = state.salesLogs.firstWhere(
        (log) => log.id == salesLogId,
        orElse: () => throw Exception('판매 기록을 찾을 수 없습니다: $salesLogId'),
      );

      // 필드 업데이트를 위한 copyWith 호출
      SalesLog updatedSalesLog = currentSalesLog;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('productName')) {
        updatedSalesLog = updatedSalesLog.copyWith(productName: fields['productName'] as String);
      }

      if (fields.containsKey('sellerName')) {
        final sellerName = fields['sellerName'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(sellerName: sellerName);
      }

      if (fields.containsKey('soldPrice')) {
        updatedSalesLog = updatedSalesLog.copyWith(soldPrice: fields['soldPrice'] as int);
      }

      if (fields.containsKey('soldQuantity')) {
        updatedSalesLog = updatedSalesLog.copyWith(soldQuantity: fields['soldQuantity'] as int);
      }

      if (fields.containsKey('totalAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(totalAmount: fields['totalAmount'] as int);
      }

      if (fields.containsKey('transactionType')) {
        updatedSalesLog = updatedSalesLog.copyWith(transactionType: fields['transactionType'] as TransactionType);
      }

      if (fields.containsKey('setDiscountAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(setDiscountAmount: fields['setDiscountAmount'] as int);
      }

      if (fields.containsKey('setDiscountNames')) {
        final setDiscountNames = fields['setDiscountNames'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(setDiscountNames: setDiscountNames);
      }

      if (fields.containsKey('manualDiscountAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(manualDiscountAmount: fields['manualDiscountAmount'] as int);
      }

      if (fields.containsKey('paymentMethod')) {
        final paymentMethod = fields['paymentMethod'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(paymentMethod: paymentMethod);
      }

      // 로컬 DB 업데이트
      await _crud.updateSalesLog(updatedSalesLog);

      // 로컬 전용 모드: 실시간 동기화 불필요

      // 관련 Provider들에게 판매 기록 업데이트 알림
      _notifyRelatedProviders(updatedSalesLog);

      // 상태 갱신
      await loadSalesLogs(showLoading: false);

      LoggerUtils.methodEnd('updateSalesLogFields', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '판매 기록 필드 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': salesLogId, 'fields': fields.keys.toList()},
      );

      if (mounted) {
        state = state.copyWith(
          errorMessage: '판매 기록 필드 업데이트에 실패했습니다: ${e.toString()}',
        );
      }
      rethrow;
    }
  }

  /// 판매자 이름 변경 시 해당 판매자의 모든 판매 기록 업데이트
  Future<void> updateSellerNameForAllSalesLogs(String oldName, String newName, int eventId) async {
    try {
      LoggerUtils.logInfo('판매자 이름 변경으로 인한 판매 기록 일괄 업데이트 시작: $oldName → $newName (eventId: $eventId)', tag: _tag);

      // 트랜잭션으로 조건부 일괄 업데이트 (eventId + sellerName)
      final db = await ref.read(databaseServiceProvider).database;
      final table = DatabaseServiceImpl.salesLogTable;
      final updatedCount = await db.update(
        table,
        {'sellerName': newName},
        where: 'eventId = ? AND sellerName = ?',
        whereArgs: [eventId, oldName],
      );

      LoggerUtils.logInfo('판매 기록 판매자 이름 업데이트 완료: $updatedCount건 갱신', tag: _tag);

      // 상태 갱신 (강제 새로고침)
      if (mounted) {
        await loadSalesLogs();
      }
    } catch (e) {
      LoggerUtils.logError('판매자 이름 변경으로 인한 판매 기록 일괄 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 관련 Provider들에게 판매 기록 업데이트 알림 (순환 호출 방지)
  void _notifyRelatedProviders(SalesLog updatedSalesLog) {
    // 순환 호출 방지: 이미 알림 중이면 건너뛰기
    if (_isNotifyingRelatedProviders) {
      LoggerUtils.logDebug('순환 호출 방지: 판매 기록 관련 Provider 알림 건너뛰기 - ${updatedSalesLog.productName}', tag: _tag);
      return;
    }

    try {
      _isNotifyingRelatedProviders = true;

      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 상품 Provider 갱신 (재고 변경이 있는 경우)
          ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

          LoggerUtils.logInfo('판매 기록 관련 Provider들 갱신 완료: ${updatedSalesLog.productName}', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('판매 기록 관련 Provider 갱신 실패', tag: _tag, error: e);
        } finally {
          // 플래그 해제 (다음 호출을 위해)
          _isNotifyingRelatedProviders = false;
        }
      });
    } catch (e) {
      _isNotifyingRelatedProviders = false; // 에러 시에도 플래그 해제
      LoggerUtils.logError('판매 기록 관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }

  /// 배치 Sales Log 추가 (판매 처리 최적화용)
  Future<List<SalesLog>> batchAddSalesLogs(List<SalesLog> salesLogs) async {
    LoggerUtils.logInfo('배치 Sales Log 추가 시작: ${salesLogs.length}개', tag: _tag);

    try {
      final savedSalesLogs = <SalesLog>[];

      // 현재 행사 확인
      final currentEvent = ref.read(currentWorkspaceProvider);
      if (currentEvent == null) {
        throw Exception('현재 행사가 선택되지 않았습니다.');
      }

      // 1. 로컬 DB 배치 저장 (중복 방지 강화)
      for (final salesLog in salesLogs) {
        final salesLogWithEventId = salesLog.copyWith(eventId: currentEvent.id);

        // 중복 체크: 같은 batchSaleId, productId, soldQuantity, saleTimestamp를 가진 기록이 있는지 확인
        final isDuplicate = await _checkDuplicateSalesLog(salesLogWithEventId);
        if (isDuplicate) {
          continue;
        }

        final savedSalesLog = await _crud.addSalesLog(salesLogWithEventId);
        savedSalesLogs.add(savedSalesLog);

        // 최근 추가한 판매 기록으로 캐시 (무한 루프 방지용)
        _recentlyAddedSalesLogs.add(savedSalesLog.id);
        // 10초 후 캐시에서 제거 (5초에서 10초로 증가)
        Future.delayed(const Duration(seconds: 10), () {
          _recentlyAddedSalesLogs.remove(savedSalesLog.id);
        });
      }

      // 2. 상태 갱신 (중복 방지)
      final currentState = state;
      final existingIds = currentState.salesLogs.map((log) => log.id).toSet();
      final newLogs = savedSalesLogs.where((log) => !existingIds.contains(log.id)).toList();
      final updatedLogs = [...newLogs, ...currentState.salesLogs];
      final updatedAllLogs = [...newLogs, ...currentState.allSalesLogs];
      final newDisplayItems = _createDisplayItems(updatedLogs);
      state = currentState.copyWith(
        salesLogs: updatedLogs,
        filteredSalesLogs: updatedLogs, // 즉시 화면 반영을 위해 동기화
        allSalesLogs: updatedAllLogs,
        displayItems: newDisplayItems,
      );

      // 3. 백그라운드에서 Firebase 배치 동기화
      _syncSalesLogsBatchInBackground(savedSalesLogs);

      LoggerUtils.logInfo('배치 Sales Log 추가 완료: ${savedSalesLogs.length}개', tag: _tag);
      return savedSalesLogs;
    } catch (e) {
      LoggerUtils.logError('배치 Sales Log 추가 실패', tag: _tag, error: e);
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'SALES_LOG_BATCH_ADD_ERROR',
        errorSeverity: 'high',
      );
      return [];
    }
  }

  /// 중복 판매 기록 체크
  Future<bool> _checkDuplicateSalesLog(SalesLog salesLog) async {
    try {
      // 현재 상태의 판매 기록들 중에서 중복 체크
      final currentLogs = state.salesLogs;

      // 같은 batchSaleId, productId, soldQuantity를 가진 기록이 최근 1분 내에 있는지 확인
      final recentTimestamp = DateTime.now().millisecondsSinceEpoch - (60 * 1000); // 1분 전

      final duplicates = currentLogs.where((log) =>
        log.batchSaleId == salesLog.batchSaleId &&
        log.productId == salesLog.productId &&
        log.soldQuantity == salesLog.soldQuantity &&
        log.saleTimestamp >= recentTimestamp
      ).toList();

      return duplicates.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('중복 판매 기록 체크 실패', tag: _tag, error: e);
      return false; // 에러 시 중복이 아닌 것으로 처리
    }
  }

  /// 배치 Sales Log Firebase 동기화 (백그라운드 - 플랜별 제한)
  void _syncSalesLogsBatchInBackground(List<SalesLog> salesLogs) {
    if (salesLogs.isEmpty) return;

    Future.microtask(() async {
      try {
        // 플랜별 서버 동기화 기능 확인
        // 로컬 전용 모드: 사용하지 않는 변수들 제거됨

        // 로컬 전용 모드: 서버 동기화 제거됨
      } catch (e) {
        LoggerUtils.logError('배치 Sales Log Firebase 동기화 실패: ${salesLogs.length}개', tag: _tag, error: e);
      }
    });
  }

  /// 완전한 판매 기록 삭제 (재고 복구 + Firebase 동기화 포함)
  ///
  /// 모든 삭제 요구사항을 충족하는 통합 메서드입니다.
  /// 이 메서드를 사용하면 재고 복구와 Firebase 동기화가 모두 처리됩니다.
  Future<String> deleteSalesLogComplete(SalesLog salesLog) async {
    if (!mounted) return '삭제 실패: 화면이 종료되었습니다.';

    try {
      return await _crud.deleteSalesLogComplete(salesLog);
    } catch (e) {
      LoggerUtils.logError('완전한 판매 기록 삭제 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 묶음 판매 기록 삭제 및 재고 복구
  Future<String> deleteGroupedSaleAndUpdateStock(GroupedSale groupedSale) async {
    return await _crud.deleteGroupedSaleAndUpdateStock(groupedSale);
  }



  /// 판매 기록 ID로 조회
  Future<SalesLog?> getSalesLogById(int id) async {
    return await _crud.getSalesLogById(id);
  }

  /// 판매자별 판매 기록 로드
  Future<void> loadSalesLogsBySeller(String sellerName) async {
    await _crud.loadSalesLogsBySeller(sellerName);
  }

  /// 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsByType(TransactionType transactionType) async {
    await _crud.loadSalesLogsByType(transactionType);
  }

  /// 판매자 및 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    await _crud.loadSalesLogsBySellerAndType(sellerName, transactionType);
  }

  /// 날짜 범위별 판매 기록 로드
  Future<void> loadSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    await _crud.loadSalesLogsByDateRange(startDate, endDate);
  }

  /// 배치 ID별 판매 기록 로드
  Future<void> loadSalesLogsByBatchId(String batchId) async {
    await _crud.loadSalesLogsByBatchId(batchId);
  }

  /// 작업 취소 메서드
  void cancelOperation(String operationName) {
    // StateNotifier에서는 단순히 상태를 업데이트하여 취소 처리
    state = state.copyWith(isCancelled: true);
  }

  /// 진행 중인 작업 취소
  void cancelCurrentOperation() {
    cancelOperation('loadSalesLogs');
    cancelOperation('processBatchOperation');
  }

  /// 거래 타입 이름 가져오기
  String getTransactionTypeName(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return '판매';
      case TransactionType.service:
        return '서비스';
      case TransactionType.discount:
        return '할인';

      case TransactionType.setDiscount:
        return '세트 할인';
    }
  }

  /// 이벤트 전환 시 모든 데이터 정리 - 메모리 누수 방지
  void _clearAllDataForEventTransition() {
    try {
      if (kDebugMode) {
        LoggerUtils.logInfo('이벤트 전환 - SalesLogNotifier 데이터 정리 시작', tag: _tag);
      }

      // 로컬 전용 모드: 실시간 구독 제거됨

      // 상태 완전 초기화
      state = const SalesLogState();

      if (kDebugMode) {
        LoggerUtils.logInfo('이벤트 전환 - SalesLogNotifier 데이터 정리 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SalesLogNotifier 데이터 정리 중 오류: $e', tag: _tag);
    }
  }

  /// 설정 저장
  Future<void> _saveSettings() async {
    try {
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 필터 설정 저장
      final filterSettings = {
        'selectedSellerFilter': state.selectedSellerFilter,
        'selectedTypeFilter': state.selectedTypeFilter?.value,
      };

      await settingsRepository.setPageFilterSettings(_pageKey, jsonEncode(filterSettings));

      LoggerUtils.logDebug('판매기록 설정 저장 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매기록 설정 저장 실패', tag: _tag, error: e);
    }
  }

  /// 설정 복원
  Future<void> _restoreSettings() async {
    try {
      final settingsRepository = ref.read(settingsRepositoryProvider);

      // 필터 설정 복원
      final filterJson = await settingsRepository.getPageFilterSettings(_pageKey);
      if (filterJson != null) {
        final filterSettings = jsonDecode(filterJson) as Map<String, dynamic>;

        final selectedSellerFilter = filterSettings['selectedSellerFilter'] as String? ?? '';
        final selectedTypeFilterValue = filterSettings['selectedTypeFilter'] as String?;
        final selectedTypeFilter = selectedTypeFilterValue != null
            ? TransactionType.values.where((t) => t.value == selectedTypeFilterValue).firstOrNull
            : null;

        state = state.copyWith(
          selectedSellerFilter: selectedSellerFilter,
          selectedTypeFilter: selectedTypeFilter,
        );

        LoggerUtils.logDebug('판매기록 설정 복원 완료: 판매자=$selectedSellerFilter, 거래유형=$selectedTypeFilter', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('판매기록 설정 복원 실패', tag: _tag, error: e);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}

final salesLogNotifierProvider = StateNotifierProvider<SalesLogNotifier, SalesLogState>((ref) {
  final notifier = SalesLogNotifier(ref);

  // StateSyncManager에 상태 캐시 등록 (상태 변경 시 자동 업데이트)
  notifier.addListener((state) {
    StateSyncManager().cacheState('sales_log', state);
  });

  // 초기 상태 캐시
  StateSyncManager().cacheState('sales_log', notifier.state);

  // Provider dispose 시 캐시 정리
  ref.onDispose(() {
    try {
      StateSyncManager().clearAllStates(); // 전체 캐시 정리
    } catch (e) {
      // 정리 실패 시 무시 (앱 종료 시점일 수 있음)
    }
  });

  return notifier;
});

// 하단의 @riverpod 함수형 Provider들도 일반 Provider로 변환 필요
final salesLogsProvider = Provider<List<SalesLog>>((ref) {
  return ref.watch(salesLogNotifierProvider).salesLogs;
});
final filteredSalesLogsProvider = Provider<List<SalesLog>>((ref) {
  return ref.watch(salesLogNotifierProvider).filteredSalesLogs;
});
final salesLogDisplayItemsProvider = Provider<List<SalesLogDisplayItem>>((ref) {
  return ref.watch(salesLogNotifierProvider).displayItems;
});
final salesLogSellerNamesProvider = Provider<List<String>>((ref) {
  return ref.watch(salesLogNotifierProvider).sellerNames;
});
final salesStatsProvider = Provider<List<SalesStatItem>>((ref) {
  return ref.watch(salesLogNotifierProvider).salesStats;
});
final salesLogIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(salesLogNotifierProvider).isLoading;
});
final salesLogErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(salesLogNotifierProvider).errorMessage;
});
final salesLogErrorCodeProvider = Provider<String?>((ref) {
  return ref.watch(salesLogNotifierProvider).errorCode;
});

/// Sales Log PaginationController Provider
// PaginationController, PaginationState, pagedSalesLogControllerProvider 등 페이징 관련 타입/Provider/변수/주석 전체 제거
// 전체 불러오기 구조로 통일
// import 누락/불필요 import 정리

/// Sales Log PaginationState Provider
// PaginationState, pagedSalesLogStateProvider 등 페이징 관련 Provider/타입/변수/주석/분기 완전 삭제
// 전체 불러오기 구조로 통일
// import 누락/불필요 import 정리

/// Sales Log 데이터 동기화 관리자
class SalesLogDataSyncManager {
  static const String _tag = 'SalesLogDataSyncManager';
  
  /// 모든 Sales Log 관련 Provider를 일관되게 갱신
  static Future<void> syncAllSalesLogData(WidgetRef ref) async {
    LoggerUtils.logInfo('Sales Log 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Sales Log Notifier 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      
      LoggerUtils.logInfo('Sales Log 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Sales Log 추가 후 동기화
  static Future<void> syncAfterAddSalesLog(WidgetRef ref, SalesLog newSalesLog) async {
    LoggerUtils.logInfo('Sales Log 추가 후 동기화 시작', tag: _tag);

    try {
      // 단순화된 Sales Log Notifier 추가
      await ref.read(salesLogNotifierProvider.notifier).addSalesLog(newSalesLog);

      LoggerUtils.logInfo('Sales Log 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }




  
  /// Sales Log 수정 후 동기화
  static Future<void> syncAfterUpdateSalesLog(WidgetRef ref, SalesLog salesLog) async {
    LoggerUtils.logInfo('Sales Log 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Sales Log Notifier 수정
      await ref.read(salesLogNotifierProvider.notifier).updateSalesLog(salesLog);
      
      LoggerUtils.logInfo('Sales Log 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Sales Log 삭제 후 동기화 (완전한 삭제 사용)
  static Future<void> syncAfterDeleteSalesLog(WidgetRef ref, SalesLog salesLog) async {
    LoggerUtils.logInfo('Sales Log 삭제 후 동기화 시작', tag: _tag);

    try {
      // 완전한 Sales Log 삭제 (재고 복구 + Firebase 동기화 포함)
      await ref.read(salesLogNotifierProvider.notifier).deleteSalesLogComplete(salesLog);

      LoggerUtils.logInfo('Sales Log 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
