import '../models/prepayment.dart';
import '../models/prepayment_sort_order.dart';
import 'base_state.dart';

/// 선입금 필터 옵션
enum PrepaymentFilter {
  all('전체'),
  received('수령 완료'),
  unreceived('미수령');

  const PrepaymentFilter(this.displayName);
  final String displayName;
}

class PrepaymentState extends BaseState {
  final List<Prepayment> prepayments;
  final List<Prepayment> filteredPrepayments;
  final PrepaymentSortOrder sortOrder;
  final bool showReceivedOnly;
  final String searchQuery;
  final PrepaymentFilter filter;

  const PrepaymentState({
    this.prepayments = const [],
    this.filteredPrepayments = const [],
    this.sortOrder = PrepaymentSortOrder.prepaymentNumberAsc,
    this.showReceivedOnly = false,
    this.searchQuery = '',
    this.filter = PrepaymentFilter.all,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  PrepaymentState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  PrepaymentState copyWith({
    List<Prepayment>? prepayments,
    List<Prepayment>? filteredPrepayments,
    PrepaymentSortOrder? sortOrder,
    bool? showReceivedOnly,
    String? searchQuery,
    PrepaymentFilter? filter,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return PrepaymentState(
      prepayments: prepayments ?? this.prepayments,
      filteredPrepayments: filteredPrepayments ?? this.filteredPrepayments,
      sortOrder: sortOrder ?? this.sortOrder,
      showReceivedOnly: showReceivedOnly ?? this.showReceivedOnly,
      searchQuery: searchQuery ?? this.searchQuery,
      filter: filter ?? this.filter,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  int get totalAmount => prepayments.fold(0, (sum, p) => sum + p.amount);
  int get unreceivedCount => prepayments.where((p) => !p.isReceived).length;
  int get receivedCount => prepayments.where((p) => p.isReceived).length;

  @override
  List<Object?> get props => [
    ...super.props,
    prepayments,
    filteredPrepayments,
    sortOrder,
    showReceivedOnly,
    searchQuery,
    filter,
  ];

  static PrepaymentState initialState() {
    return const PrepaymentState();
  }
} 
