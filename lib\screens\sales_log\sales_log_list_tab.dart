import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/sales_log/sales_log_state.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../models/product.dart';
import 'sales_log_list_business_logic.dart';
import 'sales_log_list_ui_components.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../widgets/sales_record_detail_dialog.dart';


class SalesLogListTab extends ConsumerStatefulWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;
  final TransactionType? selectedTransactionType; // 하위 호환성
  final Set<TransactionType>? selectedTransactionTypes; // 다중 선택 지원
  final String selectedSortOption;

  const SalesLogListTab({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
    this.selectedTransactionType,
    this.selectedTransactionTypes,
    this.selectedSortOption = '최신순',
  });

  @override
  ConsumerState<SalesLogListTab> createState() => _SalesLogListTabState();
}

class _SalesLogListTabState extends ConsumerState<SalesLogListTab>
    with RestorationMixin {
  final ScrollController _scrollController = ScrollController();

  @override
  String? get restorationId => 'sales_log_list_tab';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 스크롤 리스너
  void _onScroll() {
    // 무한 스크롤 제거 - 모든 데이터를 한번에 표시
  }

  /// 판매 기록 삭제 핸들러
  Future<void> _handleDeleteSalesLog(SalesLog salesLog) async {
    final deleted = await SalesLogListBusinessLogic.deleteSalesLogComplete(
      ref: ref,
      context: context,
      salesLog: salesLog,
      productCategoryMap: _buildProductCategoryMap(),
    );

    // 실제로 삭제가 완료된 경우에만 데이터 새로고침
    if (deleted) {
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
    }
  }

  /// ProductCategoryMap 생성 헬퍼 메서드
  Map<int, String>? _buildProductCategoryMap() {
    final productsAsync = ref.read(productNotifierProvider);
    final categoriesAsync = ref.read(categoryNotifierProvider);

    if (!productsAsync.isLoading && !productsAsync.hasError &&
        categoriesAsync.hasValue) {
      final products = productsAsync.products;
      final categories = categoriesAsync.value!;

      // productId -> categoryName 매핑 생성
      final productCategoryMap = <int, String>{};
      for (final product in products) {
        try {
          final category = categories.firstWhere(
            (cat) => cat.id == product.categoryId,
          );
          if (product.id != null) {
            productCategoryMap[product.id!] = category.name;
          }
        } catch (e) {
          // 카테고리를 찾을 수 없는 경우 로그만 출력하고 계속 진행
          LoggerUtils.logWarning('카테고리를 찾을 수 없습니다: ${product.categoryId} (상품: ${product.name})', tag: 'SalesLogListTab');
        }
      }
      return productCategoryMap;
    }
    return null;
  }

  /// 그룹 판매 기록 삭제 핸들러
  Future<void> _handleDeleteGroup(GroupedSale groupedSale) async {
    final deleted = await SalesLogListBusinessLogic.deleteGroupSalesLogWithStockRestore(
      ref: ref,
      context: context,
      groupedSale: groupedSale,
      productCategoryMap: _buildProductCategoryMap(),
    );
    
    // 실제로 삭제가 완료된 경우에만 데이터 새로고침
    if (deleted) {
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
    }
  }

  /// 그룹 상세 다이얼로그 표시 핸들러
  void _handleShowGroupDetail(GroupedSale groupedSale) {
    SalesLogListBusinessLogic.showGroupDetailDialog(
      context: context,
      groupedSale: groupedSale,
      selectedSeller: widget.selectedSeller,
      onItemDelete: (salesLog) => _handleDeleteSalesLog(salesLog),
      productCategoryMap: _buildProductCategoryMap(),
    );
  }

  /// 단일 판매 상세 다이얼로그 표시 핸들러
  void _handleShowSingleDetail(SalesLog salesLog) {
    SalesRecordDetailDialog.show(
      context: context,
      salesLogs: [salesLog],
      selectedSeller: widget.selectedSeller,
      productCategoryMap: null, // productCategoryMap 생성이 복잡하므로 우선 null로
      onItemDelete: (log) => _handleDeleteSalesLog(log),
    );
  }

  /// 데이터 새로고침
  void _refreshData() {
    ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
  }

  @override
  Widget build(BuildContext context) {
    final salesLogState = ref.watch(salesLogNotifierProvider);

    // 디버깅 정보 제거됨 (성능 최적화)

    if (salesLogState.isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // 카테고리/상품 기반 매핑을 상위에서 1회 생성하여 하위 아이템 깜빡임 방지
    // 안전장치: 로딩 상태가 아닌 경우에만 데이터 사용
    final productState = ref.watch(productNotifierProvider);
    final products = productState.isLoading ? <Product>[] : productState.products;
    final categories = ref.watch(currentCategoriesProvider);

    final Map<int, String> productCategoryMap = <int, String>{};
    try {
      for (final product in products) {
        if (product.id != null) {
          final matched = categories.where((c) => c.id == product.categoryId);
          if (matched.isNotEmpty) {
            productCategoryMap[product.id!] = matched.first.name;
          }
        }
      }
    } catch (e) {
      // 카테고리 매핑 실패 시 무시하고 계속 진행
      LoggerUtils.logWarning('카테고리 매핑 실패', tag: 'SalesLogListTab', error: e);
    }

    // SalesLogNotifier에서 제공하는 displayItems 사용
    final displayItems = ref.watch(salesLogDisplayItemsProvider);

    // 디버깅 정보 제거됨 (성능 최적화)

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _refreshData();
        },
        child: displayItems.isEmpty
            ? SalesLogListUiComponents.buildEmptyState(
                selectedSeller: widget.selectedSeller,
                selectedTransactionType: widget.selectedTransactionType,
              )
            : RepaintBoundary(
                child: SafeArea(
                  child: CustomScrollView(
                    controller: _scrollController,
                    restorationId: 'sales_log_list_tab_scroll',
                    slivers: [
                      // 판매 기록 리스트 (필터 UI 제거됨)
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final item = displayItems[index];
                            return Column(
                              children: [
                                _SalesLogListItem(
                                  item: item,
                                  onDelete: (salesLog) => _handleDeleteSalesLog(salesLog),
                                  onDeleteGroup: (groupedSale) => _handleDeleteGroup(groupedSale),
                                  onShowGroupDetail: (groupedSale) => _handleShowGroupDetail(groupedSale),
                                  onSingleTap: (salesLog) => _handleShowSingleDetail(salesLog),
                                  productCategoryMap: productCategoryMap,
                                ),
                                if (index < displayItems.length - 1)
                                  Divider(
                                    height: 1,
                                    thickness: 1,
                                    color: AppColors.onSurfaceVariant.withValues(alpha: 0.3),
                                  ),
                              ],
                            );
                          },
                          childCount: displayItems.length,
                        ),
                      ),

                      // 페이지네이션 컨트롤
                      SliverToBoxAdapter(
                        child: _buildPaginationControls(ref, salesLogState),
                      ),

                      // 에러 메시지
                      if (salesLogState.hasError)
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Center(
                              child: Column(
                                children: [
                                  Text(
                                    '데이터 로드 중 오류가 발생했습니다',
                                    style: TextStyle(
                                      color: Colors.red[700],
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: _refreshData,
                                    child: const Text('다시 시도'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  /// 페이지네이션 컨트롤 위젯
  Widget _buildPaginationControls(WidgetRef ref, SalesLogState salesLogState) {
    if (salesLogState.allSalesLogs.isEmpty) return const SizedBox.shrink();

    // displayItems 기준으로 페이지네이션 계산 (그룹화된 판매기록을 고려)
    final totalPages = (salesLogState.totalCount / salesLogState.pageSize).ceil();
    final currentPage = salesLogState.currentPage;

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // 페이지 크기 선택 드롭다운
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '총 ${salesLogState.totalCount}건 기록',
                style: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  color: AppColors.neutral60,
                ),
              ),
              Row(
                children: [
                  const Text(
                    '페이지당 ',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 14,
                      color: AppColors.neutral60,
                    ),
                  ),
                  DropdownButton<int>(
                    value: salesLogState.pageSize,
                    items: [10, 20, 40, 60].map((size) {
                      return DropdownMenuItem<int>(
                        value: size,
                        child: Text('$size건'),
                      );
                    }).toList(),
                    onChanged: (newSize) {
                      if (newSize != null) {
                        ref.read(salesLogNotifierProvider.notifier).changePageSize(newSize);
                      }
                    },
                    underline: Container(),
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 14,
                      color: AppColors.onSurface,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 페이지네이션 버튼들
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 이전 페이지 버튼
              IconButton(
                onPressed: currentPage > 1
                    ? () => ref.read(salesLogNotifierProvider.notifier).previousPage()
                    : null,
                icon: const Icon(Icons.chevron_left),
                tooltip: '이전 페이지',
              ),

              // 페이지 번호들
              ...List.generate(
                totalPages.clamp(0, 5), // 최대 5개 페이지 버튼만 표시
                (index) {
                  int pageNum;
                  if (totalPages <= 5) {
                    pageNum = index + 1;
                  } else {
                    // 현재 페이지 주변으로 표시
                    int start = (currentPage - 2).clamp(1, totalPages - 4);
                    pageNum = start + index;
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: ElevatedButton(
                      onPressed: pageNum == currentPage
                          ? null
                          : () => ref.read(salesLogNotifierProvider.notifier).goToPage(pageNum),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: pageNum == currentPage
                            ? AppColors.primarySeed
                            : AppColors.surface,
                        foregroundColor: pageNum == currentPage
                            ? AppColors.onPrimary
                            : AppColors.onSurface,
                        minimumSize: const Size(40, 40),
                        padding: EdgeInsets.zero,
                      ),
                      child: Text('$pageNum'),
                    ),
                  );
                },
              ),

              // 다음 페이지 버튼
              IconButton(
                onPressed: currentPage < totalPages
                    ? () => ref.read(salesLogNotifierProvider.notifier).nextPage()
                    : null,
                icon: const Icon(Icons.chevron_right),
                tooltip: '다음 페이지',
              ),
            ],
          ),

          // 페이지 정보
          Text(
            '$currentPage / $totalPages 페이지',
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
        ],
      ),
    );
  }





  // 필터 섹션 제거됨 - 앱바의 필터 버튼으로 이동
}

/// 개별 판매 기록 아이템 - 깜빡임 방지를 위한 별도 위젯
class _SalesLogListItem extends ConsumerWidget {
  final SalesLogDisplayItem item;
  final Function(SalesLog) onDelete;
  final Function(GroupedSale) onDeleteGroup;
  final Function(GroupedSale) onShowGroupDetail;
  final Function(SalesLog)? onSingleTap;
  final Map<int, String>? productCategoryMap;

  const _SalesLogListItem({
    required this.item,
    required this.onDelete,
    required this.onDeleteGroup,
    required this.onShowGroupDetail,
    this.onSingleTap,
    this.productCategoryMap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 상위에서 전달받은 productCategoryMap을 그대로 사용하여 깜빡임 최소화
    return RepaintBoundary(
      child: SalesLogListUiComponents.buildSalesLogItem(
        item,
        onDelete: onDelete,
        onDeleteGroup: onDeleteGroup,
        onShowGroupDetail: onShowGroupDetail,
        onSingleTap: onSingleTap,
        productCategoryMap: productCategoryMap,
      ),
    );
  }
}
