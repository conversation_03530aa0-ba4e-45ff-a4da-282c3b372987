import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/event_workspace.dart';
import '../providers/product_provider.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨
import '../utils/logger_utils.dart';

/// 워크스페이스 데이터 통합 관리자
/// 
/// 행사 워크스페이스 변경 시 모든 관련 데이터를 일괄적으로 동기화하는 역할을 합니다.
/// 각 Provider가 개별적으로 워크스페이스 변경을 감지하지 않고, 
/// 이 관리자가 중앙에서 모든 데이터를 관리합니다.
class WorkspaceDataManager {
  static const String _tag = 'WorkspaceDataManager';
  
  static WorkspaceDataManager? _instance;
  static WorkspaceDataManager get instance => _instance ??= WorkspaceDataManager._();
  
  WorkspaceDataManager._();

  // 현재 워크스페이스
  EventWorkspace? _currentWorkspace;
  EventWorkspace? get currentWorkspace => _currentWorkspace;

  // 로컬 전용 모드: 이전 워크스페이스 ID 사용하지 않음

  // 상태 변경 리스너들
  final List<VoidCallback> _listeners = [];
  
  // 데이터 로딩 상태
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  // 에러 상태
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  /// 워크스페이스 변경 및 모든 데이터 동기화
  Future<void> switchWorkspace(EventWorkspace? workspace, Ref ref) async {
    if (_currentWorkspace?.id == workspace?.id) return;

    try {
      LoggerUtils.logInfo('워크스페이스 변경 시작: ${_currentWorkspace?.name} → ${workspace?.name}', tag: _tag);

      _isLoading = true;
      _errorMessage = null;
      _currentWorkspace = workspace;
      _notifyListeners();

      if (workspace != null) {
        // 기본 데이터 로딩 (UI 블로킹 최소화)
        try {
          await _loadEssentialData(ref);
        } catch (e) {
          LoggerUtils.logError('필수 데이터 로딩 실패', tag: _tag, error: e);
        }

        // 무거운 작업들을 백그라운드에서 실행 (UI 블로킹 방지)
        Future.microtask(() async {
          try {
            await _loadAdditionalData(ref);
          } catch (e) {
            LoggerUtils.logError('추가 데이터 로딩 실패', tag: _tag, error: e);
          }

          try {
            await _initializeRealtimeSync(workspace, ref);
          } catch (e) {
            LoggerUtils.logError('실시간 동기화 초기화 실패', tag: _tag, error: e);
          }
        });
      } else {
        _clearAllData(ref);
      }

      LoggerUtils.logInfo('워크스페이스 변경 완료: ${workspace?.name}', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('워크스페이스 변경 실패', tag: _tag, error: e, stackTrace: stackTrace);
      _errorMessage = '워크스페이스 변경에 실패했습니다: ${e.toString()}';
    } finally {
      // 항상 로딩 상태 해제
      _isLoading = false;
      _notifyListeners();
    }
  }

  /// 필수 데이터 로드 (UI 블로킹 최소화)
  Future<void> _loadEssentialData(Ref ref) async {
    if (_currentWorkspace == null) return;

    try {
      LoggerUtils.logInfo('필수 데이터 로드 시작', tag: _tag);
      final stopwatch = Stopwatch()..start();

      // Provider들을 강제로 새로고침 (기본 상태 설정)
      LoggerUtils.logInfo('Provider 새로고침 시작', tag: _tag);
      _refreshProviders(ref);
      LoggerUtils.logInfo('Provider 새로고침 완료: ${stopwatch.elapsedMilliseconds}ms', tag: _tag);

      // 필수 데이터만 먼저 로드 (상품, 판매자)
      LoggerUtils.logInfo('필수 데이터 병렬 로드 시작', tag: _tag);
      final futures = <Future<void>>[
        _loadProductData(ref),
        _loadSellerData(ref),
      ];

      await Future.wait(futures);
      LoggerUtils.logInfo('필수 데이터 로드 완료: ${stopwatch.elapsedMilliseconds}ms', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('필수 데이터 로드 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 추가 데이터 로드 (백그라운드에서 실행)
  Future<void> _loadAdditionalData(Ref ref) async {
    if (_currentWorkspace == null) return;

    try {
      // 서버 동기화는 구글 드라이브 백업 정책으로 제거되었습니다.
      // 추가 데이터 로드 (선입금, 판매 로그)
      final futures = <Future<void>>[
        _loadPrepaymentData(ref),
        _loadSalesLogData(ref),
      ];

      await Future.wait(futures);
    } catch (e) {
      LoggerUtils.logError('추가 데이터 로드 실패', tag: _tag, error: e);
      // 추가 데이터 로드 실패해도 앱은 계속 실행
    }
  }



  /// 행사 전환 후 Provider들을 강제로 새로고침 (순환 참조 방지)
  void _refreshProviders(Ref ref) {
    try {
      LoggerUtils.logInfo('행사 전환 후 Provider 새로고침 시작', tag: _tag);

      // Provider invalidate 대신 직접 데이터 로딩으로 변경 (순환 참조 방지)
      // invalidate는 currentWorkspaceProvider를 다시 watch하게 만들어 순환 참조 발생

      LoggerUtils.logInfo('Provider 새로고침 완료 (직접 로딩 방식)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Provider 새로고침 실패', tag: _tag, error: e);
    }
  }

  /// 상품 데이터 로드
  Future<void> _loadProductData(Ref ref) async {
    try {
      final productNotifier = ref.read(productNotifierProvider.notifier);
      await productNotifier.loadProducts();
      LoggerUtils.logDebug('상품 데이터 로드 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 판매자 데이터 로드 (순환 참조 방지를 위해 직접 호출 제거)
  Future<void> _loadSellerData(Ref ref) async {
    try {
      // 순환 참조 방지를 위해 직접 Provider 호출 제거
      // SellerNotifier가 워크스페이스 변경을 감지하여 자동으로 로드됨
      LoggerUtils.logDebug('판매자 데이터는 SellerNotifier에서 자동 로드됨', tag: _tag);

      // 닉네임 기반 판매자 자동 생성은 SellerNotifier에서 처리됨

      LoggerUtils.logDebug('판매자 데이터 로드 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 선입금 데이터 로드 (순환 참조 방지를 위해 직접 호출 제거)
  Future<void> _loadPrepaymentData(Ref ref) async {
    try {
      // 순환 참조 방지를 위해 직접 Provider 호출 제거
      // PrepaymentNotifier가 워크스페이스 변경을 감지하여 자동으로 로드됨
      LoggerUtils.logDebug('선입금 데이터는 PrepaymentNotifier에서 자동 로드됨', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 판매 로그 데이터 로드
  Future<void> _loadSalesLogData(Ref ref) async {
    try {
      // 판매 로그 로딩은 현재 사용하지 않음 (컴파일 에러 방지)
      LoggerUtils.logDebug('판매 로그 데이터 로드 스킵', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 로그 데이터 로드 실패', tag: _tag, error: e);
    }
  }



  /// 모든 데이터 클리어
  void _clearAllData(Ref ref) {
    try {
      // 각 Provider의 상태를 빈 상태로 설정
      // 실제 구현에서는 각 Provider에 clearData 메서드 추가 필요
      
      LoggerUtils.logDebug('모든 데이터 클리어 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('데이터 클리어 실패', tag: _tag, error: e);
    }
  }

  /// 리스너 추가
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 리스너 제거
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 리스너들에게 상태 변경 알림
  void _notifyListeners() {
    for (final listener in _listeners) {
      try {
        listener();
      } catch (e) {
        LoggerUtils.logError('리스너 실행 중 오류', tag: _tag, error: e);
      }
    }
  }

  /// 실시간 동기화 초기화 (중복 초기화 제거)
  Future<void> _initializeRealtimeSync(EventWorkspace workspace, Ref ref) async {
    try {
      LoggerUtils.logInfo('워크스페이스 실시간 동기화 설정: ${workspace.name} (ID: ${workspace.id})', tag: _tag);

      // 실시간 동기화 서비스 가져오기 (초기화는 app_wrapper에서만 수행)
      // 로컬 전용 모드: 실시간 동기화 서비스 제거됨

      // 초기화는 하지 않고, 서비스가 준비되면 구독만 시작
      LoggerUtils.logDebug('실시간 동기화 서비스 접근 (초기화는 app_wrapper에서 수행됨)', tag: _tag);

      // 로컬 전용 모드: 실시간 구독 제거됨

      // 로컬 전용 모드: 이전 워크스페이스 ID 업데이트 건너뜀

      LoggerUtils.logInfo('워크스페이스 실시간 동기화 설정 완료: ${workspace.name}', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('워크스페이스 실시간 동기화 설정 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 실시간 동기화 실패해도 앱은 계속 실행
    }
  }

  /// 리소스 정리
  void dispose() {
    _listeners.clear();
  }
}
