import 'package:flutter_riverpod/flutter_riverpod.dart';
// 로컬 전용 모드: Firebase 관련 import 제거됨
import 'dart:async';

import '../models/seller.dart';
import '../models/nickname.dart';
import '../repositories/seller_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/provider_error_handler.dart';
// 로컬 전용 모드: 사용하지 않는 import 제거됨
import '../services/database_service.dart';
import 'base_state.dart';
import '../models/event_workspace.dart';
import '../models/subscription_plan.dart';
import 'unified_workspace_provider.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨
import 'subscription_provider.dart';
import '../mixins/workspace_aware_provider_mixin.dart';

import 'sales_log_provider.dart';
import 'nickname_provider.dart';
import 'revenue_goal_provider.dart';
import 'product_provider.dart';

// Custom Provider Exception
class CustomProviderException implements Exception {
  final String message;
  CustomProviderException(this.message);

  @override
  String toString() => message;
}

/// 판매자 상태를 관리하는 State 클래스입니다.
/// - 전체 판매자, 필터링/정렬 결과, 검색어, 로딩/에러/업데이트 상태 등 포함
/// - ProviderException, isCancelled 등 오류/취소/비동기 상태도 함께 관리
class SellerState extends BaseState {
  final List<Seller> sellers;
  final List<Seller> filteredSellers;
  final String searchQuery;
  final bool isUpdating;
  final Seller? defaultSeller;

  const SellerState({
    this.sellers = const [],
    this.filteredSellers = const [],
    this.searchQuery = '',
    this.isUpdating = false,
    this.defaultSeller,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  SellerState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SellerState copyWith({
    List<Seller>? sellers,
    List<Seller>? filteredSellers,
    String? searchQuery,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    bool? isUpdating,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
    Seller? defaultSeller,
  }) {
    return SellerState(
      sellers: sellers ?? this.sellers,
      filteredSellers: filteredSellers ?? this.filteredSellers,
      searchQuery: searchQuery ?? this.searchQuery,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      isUpdating: isUpdating ?? this.isUpdating,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
      defaultSeller: defaultSeller ?? this.defaultSeller,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    sellers,
    filteredSellers,
    searchQuery,
    isUpdating,
  ];
}

final sellerRepositoryProvider = Provider<SellerRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SellerRepository(database: databaseService);
});

class SellerNotifier extends StateNotifier<SellerState> with WorkspaceAwareProviderMixin<SellerState> {
  static const String _tag = 'SellerNotifier';
  @override
  final Ref ref;
  final bool autoInit;
  // 로컬 전용 모드: 실시간 동기화 제거됨

  // 무한 루프 방지를 위한 최근 추가한 판매자 캐시
  final Set<int> _recentlyAddedSellers = <int>{};

  // 순환 호출 방지를 위한 플래그
  bool _isNotifyingRelatedProviders = false;

  @override
  String get providerTag => 'SellerNotifier';

  SellerNotifier(this.ref, {this.autoInit = false}) : super(const SellerState()) {
    if (autoInit) {
      loadSellers();
    }
    watchCurrentEvent(); // Mixin의 메서드 사용
    _setupRealtimeSync();
    _watchNicknameChanges();
  }

  /// Mixin에서 요구하는 워크스페이스 변경 처리 메서드
  @override
  Future<void> onWorkspaceChanged(EventWorkspace? workspace) async {
    if (workspace != null) {
      await loadSellers();
      _setupRealtimeSync(); // 실시간 동기화 활성화
    } else {
      // 현재 행사 워크스페이스가 null이 되면 판매자 목록 클리어
      state = state.copyWith(
        sellers: [],
        filteredSellers: [],
        errorMessage: getNoWorkspaceErrorMessage(),
      );
    }
  }

  /// Mixin에서 요구하는 메모리 정리 메서드
  @override
  void clearDataForEventTransition() {
    _clearAllDataForEventTransition();
  }

  /// Mixin에서 요구하는 실시간 동기화 설정 메서드
  @override
  void setupRealtimeSync() {
    _setupRealtimeSync();
  }

  /// 닉네임 변경 감지 및 기본 판매자 이름 자동 업데이트
  void _watchNicknameChanges() {
    ref.listen<Nickname?>(nicknameProvider, (previous, next) {
      if (previous?.name != next?.name && next?.name != null) {
        LoggerUtils.logInfo('닉네임 변경 감지: ${previous?.name} → ${next?.name}', tag: _tag);
        _updateDefaultSellerName(previous?.name, next!.name);
      }
    });
  }

  /// 실시간 동기화 설정
  /// 로컬 전용 모드: 실시간 동기화 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드에서는 실시간 동기화를 사용하지 않음
  }

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  // 로컬 전용 모드: 사용하지 않는 메서드 제거됨

  // 로컬 전용 모드: 사용하지 않는 메서드들 제거됨

  /// 이벤트 전환 시 모든 데이터 정리 - 메모리 누수 방지
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('이벤트 전환 - SellerNotifier 데이터 정리 시작', tag: _tag);

      // 로컬 전용 모드: 실시간 구독 제거됨

      // 상태 완전 초기화
      state = const SellerState();

      LoggerUtils.logInfo('이벤트 전환 - SellerNotifier 데이터 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SellerNotifier 데이터 정리 중 오류: $e', tag: _tag);
    }
  }

  /// SellerNotifier dispose 처리
  @override
  void dispose() {
    super.dispose();
  }

  Future<void> loadSellers({bool showLoading = true}) async {
    // 🔥 실시간 구독 상태 확인 - 캐시가 아닌 실시간 상태 사용
    final subscriptionService = ref.read(subscriptionServiceProvider);
    final currentPlanType = await subscriptionService.getCurrentPlanType();
    final isProPlan = currentPlanType == SubscriptionPlanType.plus;

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'SellerNotifier');
        state = state.copyWith(
          sellers: [],
          filteredSellers: [],
          defaultSeller: null,
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요',
        );
        return;
      }

      final repository = ref.read(sellerRepositoryProvider);
      final sellers = await repository.getSellersByEventId(currentWorkspace.id);
      final defaultSeller = await repository.getDefaultSellerByEventId(currentWorkspace.id);

      // 프로 플랜이 아닌 경우 기본 판매자만 표시하고 여러 판매자 관리 기능 제한
      List<Seller> displaySellers = sellers;
      String? errorMessage;

      if (!isProPlan) {
        // 기본 판매자만 표시 (있는 경우)
        if (defaultSeller != null) {
          displaySellers = [defaultSeller];
          errorMessage = '여러 판매자 관리는 플러스 플랜에서만 이용 가능합니다.';
        } else {
          // 기본 판매자가 없으면 자동 생성 시도
          final createdSeller = await _createDefaultSellerIfNeeded(currentWorkspace.id);
          if (createdSeller != null) {
            displaySellers = [createdSeller];
            errorMessage = '여러 판매자 관리는 플러스 플랜에서만 이용 가능합니다.';
            LoggerUtils.logInfo('기본 판매자 자동 생성 완료: ${createdSeller.name}', tag: _tag);
          } else {
            displaySellers = [];
            errorMessage = '기본 판매자를 생성할 수 없습니다. 닉네임을 설정해주세요.';
            LoggerUtils.logWarning('기본 판매자 자동 생성 실패', tag: _tag);
          }
        }
        LoggerUtils.logInfo('프리 플랜 - 기본 판매자만 표시: ${displaySellers.length}개', tag: _tag);
      }

      final filteredSellers = _applyFiltersAndSort(displaySellers, searchQuery: '');
      state = state.copyWith(
        sellers: displaySellers,
        filteredSellers: filteredSellers,
        defaultSeller: defaultSeller,
        isLoading: false,
        errorMessage: errorMessage,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      // 기존 sellers 데이터는 유지
    }
  }

  /// 대표 판매자 설정
  Future<void> setDefaultSeller(int sellerId) async {
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.setDefaultSeller(sellerId);
        },
        notifier: this,
        errorCode: 'SEL_SET_DEFAULT_ERROR',
        tag: _tag,
        operationName: '대표 판매자 설정',
        setLoadingState: false,
      );
      // 성공 시 판매자 목록 새로고침
      await loadSellers();
    } catch (e) {
      // 에러 상태 유지
    }
  }

  /// 대표 판매자 해제
  Future<void> unsetDefaultSeller(int sellerId) async {
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.unsetDefaultSeller(sellerId);
        },
        notifier: this,
        errorCode: 'SEL_UNSET_DEFAULT_ERROR',
        tag: _tag,
        operationName: '대표 판매자 해제',
        setLoadingState: false,
      );
      // 성공 시 판매자 목록 새로고침
      await loadSellers();
    } catch (e) {
      // 에러 상태 유지
    }
  }

  /// 대표 판매자 여부 확인
  Future<bool> isDefaultSeller(int sellerId) async {
    try {
      final repository = ref.read(sellerRepositoryProvider);
      return await repository.isDefaultSeller(sellerId);
    } catch (e) {
      return false;
    }
  }

  List<Seller> _applyFiltersAndSort(List<Seller> sellers, {String? searchQuery}) {
    if (sellers.isEmpty) return [];
    var filtered = List<Seller>.from(sellers);
    final query = (searchQuery ?? '').trim().toLowerCase();
    if (query.isNotEmpty) {
      filtered = filtered.where((seller) {
        final name = seller.name.toLowerCase();
        final id = seller.id.toString();
        return name.contains(query) || id.contains(query);
      }).toList();
    }
    return filtered;
  }

  Future<void> searchSellers(String query) async {
    final currentState = state;
    final trimmedQuery = query.trim();
    final filteredSellers = _applyFiltersAndSort(currentState.sellers, searchQuery: trimmedQuery);
    state = currentState.copyWith(
      filteredSellers: filteredSellers,
      searchQuery: trimmedQuery,
    );
  }

  /// 판매자 추가 (로컬 DB + Firebase 동기화 포함)
  Future<void> addSeller(Seller seller) async {
    state = state.copyWith(isUpdating: true);
    bool success = false;
    try {
      // 현재 선택된 행사 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: _tag);
        state = state.copyWith(
          isUpdating: false,
          errorMessage: '행사 워크스페이스를 선택해주세요',
        );
        return;
      }

      // 판매자에 현재 워크스페이스 ID 설정
      final sellerWithEventId = seller.copyWith(eventId: currentWorkspace.id);

      int? sellerId;
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          sellerId = await repository.insertSeller(sellerWithEventId);
        },
        notifier: this,
        errorCode: 'SEL_ADD_ERROR',
        tag: _tag,
        operationName: '판매자 추가',
        setLoadingState: false,
      );

      // 최근 추가한 판매자로 캐시 (무한 루프 방지용)
      if (sellerId != null) {
        _recentlyAddedSellers.add(sellerId!);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedSellers.remove(sellerId!);
        });
      }

      // 로컬 전용 모드: 실시간 동기화 불필요

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      state = state.copyWith(isUpdating: false);
      if (success) {
        await loadSellers();
      }
    }
  }



  /// 판매자 수정 (로컬 DB + Firebase 동기화 포함)
  Future<void> updateSeller(Seller seller) async {
    bool success = false;
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.updateSeller(seller);
        },
        notifier: this,
        errorCode: 'SEL_UPDATE_ERROR',
        tag: _tag,
        operationName: '판매자 수정',
        setLoadingState: false,
      );

      // 로컬 전용 모드: 실시간 동기화 불필요

      // 관련 Provider들에게 판매자 업데이트 알림
      _notifyRelatedProviders(seller);

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      if (success) {
        await loadSellers();
      }
    }
  }

  /// 판매자의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updateSellerFields(int sellerId, Map<String, dynamic> fields) async {
    try {
      LoggerUtils.methodStart('updateSellerFields', tag: _tag, data: {'id': sellerId, 'fields': fields.keys.toList()});

      // 현재 판매자 조회
      final originalSeller = state.sellers.firstWhere((s) => s.id == sellerId);

      // 필드 업데이트를 위한 copyWith 호출
      Seller updatedSeller = originalSeller;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('name')) {
        updatedSeller = updatedSeller.copyWith(name: fields['name'] as String);
      }

      if (fields.containsKey('isDefault')) {
        updatedSeller = updatedSeller.copyWith(isDefault: fields['isDefault'] as bool);
      }

      // 로컬 DB 업데이트
      final repository = ref.read(sellerRepositoryProvider);
      await repository.updateSeller(updatedSeller);

      // 상태 업데이트
      final updatedSellers = state.sellers.map((s) {
        return s.id == updatedSeller.id ? updatedSeller : s;
      }).toList();

      state = state.copyWith(sellers: updatedSellers);

      // 판매자 이름 변경 시 관련 데이터 연동 처리
      if (fields.containsKey('name')) {
        final oldName = originalSeller.name;
        final newName = fields['name'] as String;
        if (oldName != newName) {
          await _updateRelatedDataForSellerNameChange(oldName, newName, updatedSeller.eventId);
        }
      }

      // 로컬 전용 모드: 실시간 동기화 불필요

      // 관련 Provider들에게 판매자 업데이트 알림
      _notifyRelatedProviders(updatedSeller);

      LoggerUtils.methodEnd('updateSellerFields', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '판매자 필드 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': sellerId, 'fields': fields.keys.toList()},
      );
      rethrow;
    }
  }

  /// 관련 Provider들에게 판매자 업데이트 알림 (순환 호출 방지)
  void _notifyRelatedProviders(Seller updatedSeller) {
    // 순환 호출 방지: 이미 알림 중이면 건너뛰기
    if (_isNotifyingRelatedProviders) {
      LoggerUtils.logDebug('순환 호출 방지: 판매자 관련 Provider 알림 건너뛰기 - ${updatedSeller.name}', tag: _tag);
      return;
    }

    try {
      _isNotifyingRelatedProviders = true;

      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 판매 기록 Provider 갱신 (판매자명 변경 시 필요)
          ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

          // 목표 수익 Provider 갱신 (판매자별 목표 수익이 있는 경우)
          ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);

          LoggerUtils.logInfo('판매자 관련 Provider들 갱신 완료: ${updatedSeller.name}', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('판매자 관련 Provider 갱신 실패', tag: _tag, error: e);
        } finally {
          // 플래그 해제 (다음 호출을 위해)
          _isNotifyingRelatedProviders = false;
        }
      });
    } catch (e) {
      _isNotifyingRelatedProviders = false; // 에러 시에도 플래그 해제
      LoggerUtils.logError('판매자 관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }

  Future<void> deleteSeller(int id) async {
    bool success = false;
    Seller? sellerToDelete;
    
    try {
      // 삭제할 판매자 정보 미리 저장 (실시간 동기화용)
      sellerToDelete = state.sellers.where((s) => s.id == id).firstOrNull;
      if (sellerToDelete == null) {
        LoggerUtils.logWarning('삭제할 판매자를 찾을 수 없습니다: ID $id', tag: _tag);
        return;
      }
      
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.deleteSeller(id);
        },
        notifier: this,
        errorCode: 'SEL_DELETE_ERROR',
        tag: _tag,
        operationName: '판매자 삭제',
        setLoadingState: false,
      );

      // 로컬 전용 모드: 실시간 동기화 불필요

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      if (success) {
        await loadSellers();
      }
    }
  }

  /// 기본 판매자가 없을 때 자동 생성
  Future<Seller?> _createDefaultSellerIfNeeded(int eventId) async {
    try {
      LoggerUtils.logInfo('기본 판매자 자동 생성 시도: eventId=$eventId', tag: _tag);

      // 닉네임 정보 가져오기
      final nickname = ref.read(nicknameProvider);
      if (nickname == null) {
        LoggerUtils.logInfo('닉네임이 없어 기본 판매자 자동 생성을 건너뜁니다', tag: _tag);
        return null;
      }

      // 기본 판매자 생성
      final defaultSeller = Seller.create(
        name: nickname.name,
        isDefault: true,
        eventId: eventId,
      );

      final repository = ref.read(sellerRepositoryProvider);
      final sellerId = await repository.insertSeller(defaultSeller);

      if (sellerId > 0) {
        final createdSeller = defaultSeller.copyWith(id: sellerId);
        LoggerUtils.logInfo('기본 판매자 자동 생성 완료: ${nickname.name} (ID: $sellerId)', tag: _tag);
        return createdSeller;
      }

      return null;
    } catch (e) {
      LoggerUtils.logError('기본 판매자 자동 생성 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 판매자 이름 변경 시 관련 데이터 연동 처리
  Future<void> _updateRelatedDataForSellerNameChange(String oldName, String newName, int eventId) async {
    try {
      LoggerUtils.logInfo('판매자 이름 변경 연동 처리 시작: $oldName → $newName (eventId: $eventId)', tag: _tag);

      // 1. 해당 판매자의 모든 상품 업데이트
      final productNotifier = ref.read(productNotifierProvider.notifier);
      await productNotifier.updateSellerNameForAllProducts(oldName, newName, eventId);

      // 2. 해당 판매자의 모든 판매 기록 업데이트
      final salesLogNotifier = ref.read(salesLogNotifierProvider.notifier);
      await salesLogNotifier.updateSellerNameForAllSalesLogs(oldName, newName, eventId);

      // 3. 관련 Provider들 강력한 갱신
      ref.invalidate(salesStatsProvider);
      ref.invalidate(revenueGoalStatsProvider);

      // 4. 즉시 UI 갱신을 위한 강력한 새로고침
      await Future.delayed(const Duration(milliseconds: 50)); // 데이터베이스 업데이트 완료 대기

      // 상품 Provider 강제 새로고침
      await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

      // 판매 기록 Provider 강제 새로고침
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 추가 지연으로 UI 갱신 보장
      await Future.delayed(const Duration(milliseconds: 100));

      LoggerUtils.logInfo('판매자 이름 변경 후 관련 Provider들 강력 갱신 완료', tag: _tag);

      LoggerUtils.logInfo('판매자 이름 변경 연동 처리 완료: $oldName → $newName', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 이름 변경 연동 처리 실패', tag: _tag, error: e);
      // 에러가 발생해도 판매자 이름 변경은 유지
    }
  }

  /// 닉네임 변경 시 기본 판매자 이름 업데이트
  Future<void> _updateDefaultSellerName(String? oldName, String newName) async {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) return;

      LoggerUtils.logInfo('기본 판매자 이름 업데이트 시작: $oldName → $newName', tag: _tag);

      // 현재 워크스페이스의 기본 판매자를 직접 찾기 (이름 비교 없이)
      final repository = ref.read(sellerRepositoryProvider);
      final defaultSeller = await repository.getDefaultSellerByEventId(currentWorkspace.id);

      if (defaultSeller != null) {
        LoggerUtils.logInfo('기본 판매자 발견: ${defaultSeller.name} (ID: ${defaultSeller.id})', tag: _tag);

        // 기본 판매자 이름 업데이트
        final updatedSeller = defaultSeller.copyWith(name: newName);
        await repository.updateSeller(updatedSeller);

        // 상태에서 해당 판매자만 업데이트 (전체 새로고침 방지)
        final updatedSellers = state.sellers.map((seller) {
          if (seller.id == defaultSeller.id) {
            return updatedSeller;
          }
          return seller;
        }).toList();

        // defaultSeller도 함께 업데이트
        state = state.copyWith(
          sellers: updatedSellers,
          defaultSeller: updatedSeller,
        );

        LoggerUtils.logInfo('기본 판매자 이름 업데이트 완료: ${defaultSeller.name} → $newName', tag: _tag);

        // ★ 중요: 기본 판매자 이름 변경 시 관련 상품과 판매기록도 함께 업데이트
        if (oldName != null && oldName != newName) {
          LoggerUtils.logInfo('기본 판매자 이름 변경으로 인한 관련 데이터 연동 처리 시작: $oldName → $newName', tag: _tag);
          await _updateRelatedDataForSellerNameChange(oldName, newName, currentWorkspace.id);
        }
      } else {
        LoggerUtils.logInfo('현재 워크스페이스에 기본 판매자가 없습니다. 자동 생성을 시도합니다.', tag: _tag);

        // 기본 판매자가 없으면 자동 생성
        final createdSeller = await _createDefaultSellerIfNeeded(currentWorkspace.id);
        if (createdSeller != null) {
          LoggerUtils.logInfo('기본 판매자 자동 생성 및 이름 설정 완료: $newName', tag: _tag);
          // 상태 새로고침
          await loadSellers(showLoading: false);
        }
      }
    } catch (e) {
      LoggerUtils.logError('기본 판매자 이름 업데이트 실패', tag: _tag, error: e);
    }
  }


}

final sellerNotifierProvider = StateNotifierProvider<SellerNotifier, SellerState>((ref) {
  return SellerNotifier(ref);
});

// 하단의 @riverpod 함수형 Provider들도 일반 Provider로 변환 필요
final sellersProvider = Provider<List<Seller>>((ref) {
  return ref.watch(sellerNotifierProvider).sellers;
});



final filteredSellersProvider = Provider<List<Seller>>((ref) {
  return ref.watch(sellerNotifierProvider).filteredSellers;
});
final sellerIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(sellerNotifierProvider).isLoading;
});
final sellerErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(sellerNotifierProvider).errorMessage;
});
final sellerErrorCodeProvider = Provider<String?>((ref) {
  return ref.watch(sellerNotifierProvider).errorCode;
});

/// 대표 판매자 Provider
final defaultSellerProvider = Provider<Seller?>((ref) {
  return ref.watch(sellerNotifierProvider).defaultSeller;
});

/// 대표 판매자 이름 Provider
final defaultSellerNameProvider = Provider<String>((ref) {
  final defaultSeller = ref.watch(sellerNotifierProvider).defaultSeller;
  return defaultSeller?.name ?? '';
});

/// Seller 데이터 동기화 관리자
class SellerDataSyncManager {
  static const String _tag = 'SellerDataSyncManager';
  
  /// 모든 Seller 관련 Provider를 일관되게 갱신
  static Future<void> syncAllSellerData(WidgetRef ref) async {
    LoggerUtils.logInfo('Seller 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 갱신
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
      
      LoggerUtils.logInfo('Seller 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 추가 후 동기화
  static Future<void> syncAfterAddSeller(WidgetRef ref, Seller seller) async {
    LoggerUtils.logInfo('Seller 추가 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 추가
      await ref.read(sellerNotifierProvider.notifier).addSeller(seller);
      
      LoggerUtils.logInfo('Seller 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 수정 후 동기화
  static Future<void> syncAfterUpdateSeller(WidgetRef ref, Seller seller) async {
    LoggerUtils.logInfo('Seller 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 수정
      await ref.read(sellerNotifierProvider.notifier).updateSeller(seller);
      
      LoggerUtils.logInfo('Seller 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 삭제 후 동기화
  static Future<void> syncAfterDeleteSeller(WidgetRef ref, int id) async {
    LoggerUtils.logInfo('Seller 삭제 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 삭제
      await ref.read(sellerNotifierProvider.notifier).deleteSeller(id);
      
      LoggerUtils.logInfo('Seller 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

