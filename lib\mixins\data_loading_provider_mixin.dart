// 바라 부스 매니저 - 데이터 로딩 Provider Mixin
// 중복 로딩 패턴 최적화를 위한 공통 Mixin

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';

/// 데이터 로딩 패턴을 표준화하는 Mixin
/// 
/// 사용법:
/// ```dart
/// class MyNotifier extends StateNotifier<MyState> with DataLoadingProviderMixin<MyState> {
///   @override
///   String get providerTag => 'MyNotifier';
///   
///   @override
///   Future<List<MyData>> performDataLoad() async {
///     // 실제 데이터 로딩 로직
///     return await repository.loadData();
///   }
///   
///   @override
///   MyState updateStateWithData(MyState currentState, List<MyData> data) {
///     return currentState.copyWith(data: data, isLoading: false);
///   }
/// }
/// ```
mixin DataLoadingProviderMixin<T> on StateNotifier<T> {
  /// Provider의 태그 (로깅용)
  String get providerTag;
  
  /// 실제 데이터 로딩을 수행하는 메서드 (하위 클래스에서 구현)
  Future<dynamic> performDataLoad();
  
  /// 로딩된 데이터로 상태를 업데이트하는 메서드 (하위 클래스에서 구현)
  T updateStateWithData(T currentState, dynamic data);
  
  /// 에러 상태로 업데이트하는 메서드 (하위 클래스에서 구현)
  T updateStateWithError(T currentState, String errorMessage);
  
  /// 로딩 상태로 업데이트하는 메서드 (하위 클래스에서 구현)
  T updateStateWithLoading(T currentState, bool isLoading);
  
  /// 중복 로딩 방지를 위한 플래그
  bool _isLoading = false;
  
  /// 표준화된 데이터 로딩 메서드
  Future<void> loadData({bool showLoading = true}) async {
    // 중복 로딩 방지
    if (_isLoading) {
      LoggerUtils.logDebug('이미 로딩 중 - 중복 로딩 방지: $providerTag', tag: providerTag);
      return;
    }
    
    _isLoading = true;
    
    try {
      LoggerUtils.methodStart('loadData', tag: providerTag);
      
      // 로딩 상태 설정
      if (showLoading) {
        state = updateStateWithLoading(state, true);
      }
      
      // 실제 데이터 로딩
      final data = await performDataLoad();
      
      // 상태 업데이트
      if (mounted) {
        state = updateStateWithData(state, data);
      }
      
      LoggerUtils.methodEnd('loadData', tag: providerTag);
      
    } catch (e, stackTrace) {
      LoggerUtils.logError('데이터 로딩 실패', tag: providerTag, error: e, stackTrace: stackTrace);
      
      if (mounted) {
        state = updateStateWithError(state, e.toString());
      }
    } finally {
      _isLoading = false;
      
      // 안전장치: 로딩 상태가 계속 유지되지 않도록 보장
      if (mounted) {
        state = updateStateWithLoading(state, false);
      }
    }
  }
  
  /// 데이터 새로고침 (로딩 표시 없이)
  Future<void> refreshData() async {
    await loadData(showLoading: false);
  }
  
  /// 로딩 중인지 확인
  bool get isCurrentlyLoading => _isLoading;
  
  /// 재시도 로직이 포함된 데이터 로딩
  Future<void> loadDataWithRetry({
    bool showLoading = true,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;
    
    while (retryCount <= maxRetries) {
      try {
        await loadData(showLoading: showLoading && retryCount == 0);
        return; // 성공하면 종료
      } catch (e) {
        retryCount++;
        
        if (retryCount > maxRetries) {
          rethrow; // 최대 재시도 횟수 초과 시 예외 재발생
        }
        
        LoggerUtils.logWarning(
          '데이터 로딩 실패 - 재시도 $retryCount/$maxRetries: $e',
          tag: providerTag,
        );
        
        // 재시도 전 대기
        await Future.delayed(retryDelay * retryCount);
      }
    }
  }
  
  /// 조건부 데이터 로딩 (이미 데이터가 있으면 스킵)
  Future<void> loadDataIfNeeded({
    required bool Function(T state) hasData,
    bool showLoading = true,
  }) async {
    if (hasData(state)) {
      LoggerUtils.logDebug('데이터가 이미 존재 - 로딩 스킵: $providerTag', tag: providerTag);
      return;
    }
    
    await loadData(showLoading: showLoading);
  }
}
