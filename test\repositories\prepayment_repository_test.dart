import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/models/prepayment.dart';
import 'package:parabara/models/prepayment_sort_order.dart';
import 'package:parabara/repositories/prepayment_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;
  late PrepaymentRepository repository;

  setUpAll(() {
    // SQLite 초기화
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    // 인메모리 데이터베이스 생성
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE prepayments (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              buyerName TEXT NOT NULL,
              buyerContact TEXT,
              amount INTEGER NOT NULL,
              pickupDayOfWeek TEXT NOT NULL,
              productNameList TEXT NOT NULL,
              memo TEXT,
              registrationDate TEXT NOT NULL,
              isReceived INTEGER DEFAULT 0,
              registrationActualDayOfWeek INTEGER NOT NULL,
              bankName TEXT,
              email TEXT,
              twitterAccount TEXT,
              registrationTimestamp INTEGER NOT NULL,
              purchasedProductsJson TEXT DEFAULT '{}',
              eventId INTEGER NOT NULL DEFAULT 1,
              orderNumber TEXT,
              prepaymentNumber INTEGER
            )
          ''');
        },
      ),
    );

    // 테스트용 DatabaseService 구현
    databaseService = TestDatabaseService(db);
    repository = PrepaymentRepository(database: databaseService);

    // 테스트 데이터 삽입
    await db.insert('prepayments', {
      'buyerName': 'Test User 1',
      'buyerContact': '010-1234-5678',
      'amount': 10000,
      'pickupDayOfWeek': 'Monday',
      'productNameList': 'Product A, Product B',
      'registrationDate': '2024-03-20',
      'registrationActualDayOfWeek': 3,
      'bankName': 'Test Bank',
      'email': '<EMAIL>',
      'registrationTimestamp': DateTime.now().millisecondsSinceEpoch,
    });
  });

  tearDown(() async {
    await db.close();
  });

  group('PrepaymentRepository SQL Injection Tests', () {
    test('searchPrepayments should safely handle malicious input', () async {
      // SQL Injection 시도가 포함된 검색어
      final maliciousQueries = [
        "' OR '1'='1",
        "'; DROP TABLE prepayments; --",
        "' UNION SELECT * FROM sqlite_master; --",
        "' OR buyerName LIKE '%",
        "\\'; DELETE FROM prepayments; --",
      ];

      for (final query in maliciousQueries) {
        final results = await repository.searchPrepayments(query);

        // 결과가 비어있거나, 정상적인 검색 결과만 포함해야 함
        for (final prepayment in results) {
          expect(prepayment, isA<Prepayment>());
          expect(prepayment.buyerName, isNot(contains("'")));
          expect(prepayment.buyerName, isNot(contains(";")));
          expect(prepayment.buyerName, isNot(contains("--")));
        }
      }
    });

    test(
      'getPrepaymentsSorted should safely handle malicious sort orders',
      () async {
        // 모든 정렬 옵션에 대해 테스트
        for (final sortOrder in PrepaymentSortOrder.values) {
          final results = await repository.getPrepaymentsSorted(sortOrder);

          // 결과가 정상적인 Prepayment 객체들만 포함해야 함
          for (final prepayment in results) {
            expect(prepayment, isA<Prepayment>());
          }
        }
      },
    );
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;


}
