// 바라 부스 매니저 - 워크스페이스 인식 Provider Mixin
// 중복 코드 패턴 최적화를 위한 공통 Mixin

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/event_workspace.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/logger_utils.dart';

/// 워크스페이스 변경을 감지하고 자동으로 데이터를 새로고침하는 Mixin
/// 
/// 사용법:
/// ```dart
/// class MyNotifier extends StateNotifier<MyState> with WorkspaceAwareProviderMixin<MyState> {
///   @override
///   String get providerTag => 'MyNotifier';
///   
///   @override
///   Future<void> onWorkspaceChanged(EventWorkspace? workspace) async {
///     if (workspace != null) {
///       await loadMyData();
///     } else {
///       clearMyData();
///     }
///   }
/// }
/// ```
mixin WorkspaceAwareProviderMixin<T> on StateNotifier<T> {
  /// Provider의 태그 (로깅용)
  String get providerTag;
  
  /// Riverpod Ref 객체
  Ref get ref;
  
  /// 워크스페이스 변경 시 호출되는 메서드 (하위 클래스에서 구현)
  Future<void> onWorkspaceChanged(EventWorkspace? workspace);
  
  /// 워크스페이스가 null이 될 때 호출되는 메서드 (선택적 구현)
  void onWorkspaceCleared() {
    // 기본 구현: 아무것도 하지 않음
    // 하위 클래스에서 필요시 오버라이드
  }
  
  /// 행사 전환 시 메모리 정리 메서드 (선택적 구현)
  void clearDataForEventTransition() {
    // 기본 구현: 아무것도 하지 않음
    // 하위 클래스에서 필요시 오버라이드
  }
  
  /// 실시간 동기화 설정 메서드 (선택적 구현)
  void setupRealtimeSync() {
    // 로컬 전용 모드에서는 기본적으로 아무것도 하지 않음
    // 하위 클래스에서 필요시 오버라이드
  }
  
  /// 워크스페이스 변경 감지 및 자동 새로고침 설정
  void watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) async {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo(
          '현재 행사 워크스페이스 변경 감지 - $providerTag 새로고침: ${previous?.name} -> ${next?.name}', 
          tag: providerTag
        );
        
        // 행사 전환 시 메모리 정리
        clearDataForEventTransition();
        
        if (next != null) {
          // 새 워크스페이스로 데이터 로드
          await onWorkspaceChanged(next);
          
          // 실시간 동기화 재설정
          setupRealtimeSync();
        } else {
          // 워크스페이스가 null이 된 경우
          onWorkspaceCleared();
          await onWorkspaceChanged(null);
        }
      }
    });
  }
  
  /// 현재 워크스페이스 가져오기 (편의 메서드)
  EventWorkspace? getCurrentWorkspace() {
    return ref.read(currentWorkspaceProvider);
  }
  
  /// 현재 워크스페이스 ID 가져오기 (편의 메서드)
  int? getCurrentWorkspaceId() {
    return getCurrentWorkspace()?.id;
  }
  
  /// 워크스페이스가 설정되어 있는지 확인 (편의 메서드)
  bool hasCurrentWorkspace() {
    return getCurrentWorkspace() != null;
  }
  
  /// 표준 에러 메시지 생성 (편의 메서드)
  String getNoWorkspaceErrorMessage() {
    return '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.';
  }
}
