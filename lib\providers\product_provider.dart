import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product.dart';
import '../models/product_sort_option.dart';
import '../utils/logger_utils.dart';
import '../repositories/product_repository.dart';
import 'product_notifier.dart';
import 'product_state.dart';
import '../services/database_service.dart';

export 'product_notifier.dart';
export 'product_state.dart';

/// 상품 데이터베이스 접근을 위한 Repository Provider입니다.
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final db = ref.watch(databaseServiceProvider);
  return ProductRepository(database: db);
});

/// 상품 상태를 관리하는 Provider입니다.
final productNotifierProvider = StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  final repository = ref.watch(productRepositoryProvider);
  final notifier = ProductNotifier(repository, ref, autoInit: false); // 자동 초기화 비활성화

  // 자동 초기화 제거 - currentEventProvider가 설정된 후에만 수동으로 로딩
  // Future.microtask() 자동 초기화 제거하여 타이밍 이슈 해결

  return notifier;
});

/// 개별 상품을 조회하는 Provider입니다. (성능 최적화)
final productProvider = Provider.family<Product?, int>((ref, productId) {
  final products = ref.watch(filteredProductsProvider);
  return products.where((product) => product.id == productId).firstOrNull;
});

/// 상품 목록을 제공하는 Provider입니다. (성능 최적화)
final filteredProductsProvider = Provider<List<Product>>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.filteredProducts));
});

/// 판매자 이름 목록을 제공하는 Provider입니다. (성능 최적화)
final sellerNamesProvider = Provider<List<String>>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.sellerNames));
});

/// 선택된 판매자 필터를 제공하는 Provider입니다. (성능 최적화)
final selectedSellerFilterProvider = Provider<String>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.selectedSellerFilter));
});

/// 현재 정렬 옵션을 제공하는 Provider입니다. (성능 최적화)
final currentSortOptionProvider = Provider<ProductSortOption>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.currentSortOption));
});

/// 상품 로딩 상태를 제공하는 Provider입니다. (성능 최적화)
final productIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.isLoading));
});

/// 상품 에러 메시지를 제공하는 Provider입니다. (성능 최적화)
final productErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.errorMessage));
});

/// 상품 개수를 제공하는 Provider입니다. (성능 최적화)
final productCountProvider = Provider<int>((ref) {
  return ref.watch(filteredProductsProvider).length;
});

/// 전체 상품 개수를 제공하는 Provider입니다. (성능 최적화)
final totalProductCountProvider = Provider<int>((ref) {
  return ref.watch(productNotifierProvider.select((state) => state.products.length));
});

/// 성능 최적화를 위한 추가 Provider들

/// 재고 부족 상품 개수를 제공하는 Provider입니다.
final outOfStockProductCountProvider = Provider<int>((ref) {
  return ref.watch(filteredProductsProvider)
      .where((product) => product.quantity <= 0)
      .length;
});

/// 재고 있는 상품 개수를 제공하는 Provider입니다.
final inStockProductCountProvider = Provider<int>((ref) {
  return ref.watch(filteredProductsProvider)
      .where((product) => product.quantity > 0)
      .length;
});

/// 상품 총 가치를 제공하는 Provider입니다.
final totalProductValueProvider = Provider<double>((ref) {
  return ref.watch(filteredProductsProvider)
      .fold(0.0, (sum, product) => sum + (product.price * product.quantity));
});

/// 상품 데이터 변경 시 모든 관련 Provider를 갱신하는 통합 시스템
class ProductDataSyncManager {
  static const String _tag = 'ProductDataSyncManager';
  
  static Future<void> syncAllProductData(Ref ref) async {
    LoggerUtils.logInfo('Product 데이터 동기화 시작', tag: _tag);
    
    try {
      // Product Notifier 갱신
      await ref.read(productNotifierProvider.notifier).loadProducts();
      
      LoggerUtils.logInfo('Product 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Product 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  static Future<void> syncAllProductDataAsync(Ref ref) async {
    await syncAllProductData(ref);
  }
  
  /// Product 추가 후 동기화
  static Future<void> syncAfterAddProduct(Ref ref, Product product) async {
    LoggerUtils.logInfo('Product 추가 후 동기화 시작', tag: _tag);
    
    try {
      // Product Notifier에 추가
      await ref.read(productNotifierProvider.notifier).addProduct(product);
      
      LoggerUtils.logInfo('Product 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Product 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Product 수정 후 동기화
  static Future<void> syncAfterUpdateProduct(Ref ref, Product product) async {
    LoggerUtils.logInfo('Product 수정 후 동기화 시작', tag: _tag);
    
    try {
      // Product Notifier에서 수정
      await ref.read(productNotifierProvider.notifier).updateProduct(product);
      
      LoggerUtils.logInfo('Product 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Product 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Product 삭제 후 동기화
  static Future<void> syncAfterDeleteProduct(Ref ref, int id) async {
    LoggerUtils.logInfo('Product 삭제 후 동기화 시작', tag: _tag);
    
    try {
      // 1. ProductNotifier에서 해당 Product 찾기
      final product = ref.read(productNotifierProvider).products.firstWhere(
        (p) => p.id == id,
        orElse: () => throw Exception('Product with id $id not found'),
      );
      
      // ProductNotifier에서 삭제
      await ref.read(productNotifierProvider.notifier).deleteProduct(product);
      
      LoggerUtils.logInfo('Product 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Product 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
