import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../providers/settings_provider.dart';

/// 엑셀 폼 타입 및 요일 수집 설정 선택 다이얼로그
class FormTypeSelectionDialog extends ConsumerStatefulWidget {
  const FormTypeSelectionDialog({super.key});

  static Future<Map<String, dynamic>?> show(BuildContext context) {
    return showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const FormTypeSelectionDialog(),
    );
  }

  @override
  ConsumerState<FormTypeSelectionDialog> createState() => _FormTypeSelectionDialogState();
}

class _FormTypeSelectionDialogState extends ConsumerState<FormTypeSelectionDialog> {
  bool _collectDayOfWeek = false;
  final TextEditingController _dayOfWeekColumnController = TextEditingController();
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  @override
  void dispose() {
    _dayOfWeekColumnController.dispose();
    super.dispose();
  }

  /// 현재 설정 로드
  void _loadCurrentSettings() {
    final settingsState = ref.read(settingsNotifierProvider).value;
    if (settingsState != null) {
      _collectDayOfWeek = settingsState.collectDayOfWeekFromExcel;
      if (settingsState.excelDayOfWeekColumnIndex >= 0) {
        _dayOfWeekColumnController.text = _indexToColumnLetter(settingsState.excelDayOfWeekColumnIndex);
      }
    }
  }

  /// 요일 수집 설정 도움말 다이얼로그 표시
  void _showDayOfWeekHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '요일 수집 설정 도움말',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: const Text(
          '윗치폼에서 추가 질문 받기에 [수령할 요일을 N요일로 기재해주세요]를 필수 질문으로 설정하면,\n'
          '요일도 자동 수집이 가능합니다. 해당 항목이 없으면 불가능합니다.\n\n'
          '윗치폼에서 받은 엑셀에서 해당 질문이 있는 열을 확인하고 기재해주세요.',
          style: TextStyle(
            fontSize: 14,
            height: 1.4,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  /// 인덱스를 열 문자로 변환 (0 -> A, 1 -> B, ...)
  String _indexToColumnLetter(int index) {
    if (index < 0) return '';
    int n = index + 1;
    String s = '';
    while (n > 0) {
      int rem = (n - 1) % 26;
      s = String.fromCharCode(65 + rem) + s;
      n = (n - 1) ~/ 26;
    }
    return s;
  }

  /// 열 문자를 인덱스로 변환 (A -> 0, B -> 1, ...)
  int _columnLetterToIndex(String letter) {
    if (letter.isEmpty) return -1;
    letter = letter.toUpperCase();
    int result = 0;
    for (int i = 0; i < letter.length; i++) {
      result = result * 26 + (letter.codeUnitAt(i) - 64);
    }
    return result - 1;
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.description,
            color: AppColors.primarySeed,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text(
            '엑셀 설정',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      content: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: isTablet ? 450 : 350,
          maxHeight: MediaQuery.of(context).size.height * 0.6, // 키패드 공간 고려
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            const Text(
              '업로드할 엑셀 파일의 폼 타입을 선택하고, 요일 수집 설정을 구성해주세요.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),

            // 폼 타입 선택 섹션
            const Text(
              '폼 타입 선택',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            // 슬림폼 선택 카드
            _buildFormTypeCard(
              context: context,
              title: '슬림폼',
              description: '슬림폼 사용자 엑셀 등록',
              icon: Icons.view_list,
              color: Colors.blue,
              onTap: () => _handleFormTypeSelection('slim'),
            ),

            const SizedBox(height: 12),

            // 페이폼 선택 카드
            _buildFormTypeCard(
              context: context,
              title: '페이폼',
              description: '페이폼 사용자 엑셀 등록',
              icon: Icons.receipt_long,
              color: Colors.green,
              onTap: () => _handleFormTypeSelection('payform'),
            ),

            const SizedBox(height: 24),

            // 요일 수집 설정 섹션
            const Text(
              '요일 수집 설정',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            _buildWeekdaySettings(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
      ],
    );
  }

  Widget _buildFormTypeCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: color.withOpacity(0.05),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 요일 수집 설정 UI 구성
  Widget _buildWeekdaySettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Switch(
                value: _collectDayOfWeek,
                onChanged: (value) {
                  setState(() {
                    _collectDayOfWeek = value;
                    if (!value) {
                      _dayOfWeekColumnController.clear();
                      _errorText = null;
                    }
                  });
                },
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  '엑셀에서 요일 수집',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(
                  Icons.help_outline,
                  size: 20,
                  color: Colors.grey,
                ),
                onPressed: _showDayOfWeekHelpDialog,
                tooltip: '도움말',
              ),
            ],
          ),
          if (_collectDayOfWeek) ...[
            const SizedBox(height: 12),
            TextField(
              controller: _dayOfWeekColumnController,
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                labelText: '요일 열 (A,B,C...)',
                isDense: true,
                errorText: _errorText,
                helperText: '요일이 들어있는 열의 문자 (예: C)',
                border: const OutlineInputBorder(),
              ),
              onChanged: (_) {
                if (_errorText != null) {
                  setState(() {
                    _errorText = null;
                  });
                }
              },
            ),
          ],
        ],
      ),
    );
  }

  /// 폼 타입 선택 처리
  void _handleFormTypeSelection(String formType) async {
    // 요일 수집 설정이 활성화된 경우 유효성 검사
    if (_collectDayOfWeek) {
      final columnText = _dayOfWeekColumnController.text.trim().toUpperCase();
      if (columnText.isEmpty) {
        setState(() {
          _errorText = '요일 열을 입력해주세요';
        });
        return;
      }

      // 열 문자 유효성 검사
      if (!RegExp(r'^[A-Z]+$').hasMatch(columnText)) {
        setState(() {
          _errorText = '올바른 열 문자를 입력해주세요 (예: A, B, C)';
        });
        return;
      }
    }

    // 설정 저장
    try {
      await ref.read(settingsNotifierProvider.notifier).setCollectDayOfWeekFromExcel(_collectDayOfWeek);

      if (_collectDayOfWeek && _dayOfWeekColumnController.text.trim().isNotEmpty) {
        final columnIndex = _columnLetterToIndex(_dayOfWeekColumnController.text.trim());
        await ref.read(settingsNotifierProvider.notifier).setExcelDayOfWeekColumnIndex(columnIndex);
      }
    } catch (e) {
      // 설정 저장 실패 시에도 계속 진행
    }

    // 결과 반환
    Navigator.of(context).pop({
      'formType': formType,
      'collectDayOfWeek': _collectDayOfWeek,
      'dayOfWeekColumnIndex': _collectDayOfWeek && _dayOfWeekColumnController.text.trim().isNotEmpty
          ? _columnLetterToIndex(_dayOfWeekColumnController.text.trim())
          : -1,
    });
  }
}
