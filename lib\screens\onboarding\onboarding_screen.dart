import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../widgets/onboarding_components.dart';

/// 온보딩 첫 페이지 - 웜톤 디자인으로 개선
///
/// 앱 아이콘의 주황/베이지 톤을 활용한 창작자 친화적 디자인
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class OnboardingScreen extends StatefulWidget {
  final VoidCallback onStart;
  const OnboardingScreen({super.key, required this.onStart});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 세로모드로 고정 (부드러운 전환을 위해 즉시 설정)
    OrientationHelper.enterPortraitMode();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 애니메이션 시작
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildResponsiveLayout(context),
            ),
          ),
        ),
      ),
    );
  }

  /// 반응형 레이아웃 구성
  Widget _buildResponsiveLayout(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final isTablet = ResponsiveHelper.isTablet(context);

    if (isLandscape && isTablet) {
      return _buildLandscapeLayout(context);
    } else {
      return _buildPortraitLayout(context);
    }
  }

  /// 세로 모드 레이아웃
  Widget _buildPortraitLayout(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: ResponsiveHelper.getScreenPadding(context),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxContentWidth(context),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 상단 여백
              SizedBox(height: ResponsiveHelper.getSectionSpacing(context)),

              // 메인 아이콘 (앱 아이콘 스타일)
              _buildMainIcon(context),

              OnboardingComponents.buildSectionSpacing(context),

              // 타이틀
              OnboardingComponents.buildTitle(
                context: context,
                text: '바라 부스 매니저',
              ),

              OnboardingComponents.buildSmallSpacing(context),

              // 서브타이틀
              OnboardingComponents.buildSubtitle(
                context: context,
                text: '창작자를 위한 행사 부스\n통합 관리 어플리케이션',
              ),

              OnboardingComponents.buildSectionSpacing(context),

              // 기능 소개 카드들
              _buildFeatureCards(context),

              OnboardingComponents.buildSectionSpacing(context),

              // 시작 버튼
              OnboardingComponents.buildPrimaryButton(
                context: context,
                text: '시작하기',
                onPressed: widget.onStart,
                icon: Icons.arrow_forward,
              ),

              // 하단 여백
              SizedBox(height: ResponsiveHelper.getSectionSpacing(context)),
            ],
          ),
        ),
      ),
    );
  }

  /// 가로 모드 레이아웃 (태블릿용)
  Widget _buildLandscapeLayout(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: ResponsiveHelper.getScreenPadding(context),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxContentWidth(context),
          ),
          child: Row(
            children: [
              // 왼쪽: 아이콘과 텍스트
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildMainIcon(context),
                    OnboardingComponents.buildSectionSpacing(context),
                    OnboardingComponents.buildTitle(
                      context: context,
                      text: '바라 부스 매니저',
                    ),
                    OnboardingComponents.buildSmallSpacing(context),
                    OnboardingComponents.buildSubtitle(
                      context: context,
                      text: '창작자를 위한 행사 부스\n통합 관리 어플리케이션',
                    ),
                  ],
                ),
              ),

              SizedBox(width: ResponsiveHelper.getSectionSpacing(context)),

              // 오른쪽: 기능 소개와 버튼
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildFeatureCards(context),
                    OnboardingComponents.buildSectionSpacing(context),
                    OnboardingComponents.buildPrimaryButton(
                      context: context,
                      text: '시작하기',
                      onPressed: widget.onStart,
                      icon: Icons.arrow_forward,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 메인 아이콘 (앱 아이콘 스타일의 곰 캐릭터 느낌)
  Widget _buildMainIcon(BuildContext context) {
    final iconSize = ResponsiveHelper.getMainIconSize(context);

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 1.5),
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 1.5),
        child: Image.asset(
          'assets/images/bara_icon.png',
          width: iconSize * 0.8,
          height: iconSize * 0.8,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // 이미지 로드 실패 시 기본 아이콘 표시
            return Stack(
              alignment: Alignment.center,
              children: [
                // 배경 패턴 (박스 느낌)
                Container(
                  width: iconSize * 0.6,
                  height: iconSize * 0.5,
                  decoration: BoxDecoration(
                    color: AppColors.onboardingAccentLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                // 메인 아이콘
                Icon(
                  Icons.store_rounded,
                  size: iconSize * 0.4,
                  color: AppColors.onboardingTextOnPrimary,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 기능 소개 카드들
  Widget _buildFeatureCards(BuildContext context) {
    final features = [
      {'icon': Icons.dashboard_rounded, 'label': '대시보드 및 상세한 통계', 'desc': '한눈에 확인 가능한 행사 현황과 정밀한 판매 데이터 분석'},
      {'icon': Icons.point_of_sale_rounded, 'label': 'POS 판매 시스템', 'desc': '직관적이고 부스 전용 기능이 탑재된 판매 시스템'},
      {'icon': Icons.inventory_2_rounded, 'label': '선입금 관리', 'desc': '간편한 선입금 데이터 등록 및 관리'},
      {'icon': Icons.checklist_rounded, 'label': '행사 부스 전용 기능들', 'desc': '체크리스트, 서비스, 세트할인, 판매자별 관리 등 행사 부스에 필요한 전용 기능들'},
    ];

    return Column(
      children: [
        for (int i = 0; i < features.length; i++) ...[
          _buildFeatureCard(
            context: context,
            icon: features[i]['icon'] as IconData,
            label: features[i]['label'] as String,
            description: features[i]['desc'] as String,
          ),
          if (i < features.length - 1)
            SizedBox(height: ResponsiveHelper.getSectionSpacing(context) * 0.5),
        ],
      ],
    );
  }

  /// 개별 기능 카드
  Widget _buildFeatureCard({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String description,
  }) {
    return Container(
      width: double.infinity,
      padding: ResponsiveHelper.getCardPadding(context),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 아이콘
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: AppColors.accentGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.onboardingTextOnPrimary,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // 텍스트
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getBodyFontSize(context),
                    fontWeight: FontWeight.w600,
                    color: AppColors.onboardingTextPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    color: AppColors.onboardingTextSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
