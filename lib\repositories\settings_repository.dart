import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger_utils.dart';

class SettingsRepository {
  static const String _eventDayOfWeekKey = 'event_day_of_week';
  static const int _defaultEventDayOfWeek = 7; // 일요일

  // 행사 요일 저장
  Future<bool> setEventDayOfWeek(int dayOfWeek) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setInt(_eventDayOfWeekKey, dayOfWeek);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set event day of week',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 행사 요일 조회
  Future<int> getEventDayOfWeek() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_eventDayOfWeekKey) ?? _defaultEventDayOfWeek;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get event day of week',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 설정 초기화
  Future<bool> clearSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.clear();
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to clear settings',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 특정 키 삭제
  Future<bool> remove(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to remove key',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 모든 설정 키 목록 조회
  Future<Set<String>> getKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getKeys();
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get keys',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 문자열 값 저장
  Future<bool> setString(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(key, value);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set string',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 문자열 값 조회
  Future<String?> getString(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get string',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 정수 값 저장
  Future<bool> setInt(String key, int value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setInt(key, value);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set int',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 정수 값 조회
  Future<int?> getInt(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get int',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 불린 값 저장
  Future<bool> setBool(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(key, value);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set bool',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 불린 값 조회
  Future<bool?> getBool(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get bool',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 더블 값 저장
  Future<bool> setDouble(String key, double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setDouble(key, value);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set double',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 더블 값 조회
  Future<double?> getDouble(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get double',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 문자열 리스트 저장
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setStringList(key, value);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set string list',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 문자열 리스트 조회
  Future<List<String>?> getStringList(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(key);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get string list',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 엑셀에서 요일 수집 설정 저장
  Future<bool> setCollectDayOfWeekFromExcel(bool value) async {
    try {
      LoggerUtils.logInfo('요일 수집 설정 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool('collect_day_of_week_from_excel', value);
      LoggerUtils.logInfo('요일 수집 설정 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set collect day of week from excel',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 엑셀에서 요일 수집 설정 조회
  Future<bool?> getCollectDayOfWeekFromExcel() async {
    try {
      LoggerUtils.logInfo('요일 수집 설정 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getBool('collect_day_of_week_from_excel');
      LoggerUtils.logInfo('요일 수집 설정 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get collect day of week from excel',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 엑셀 요일 열 인덱스 저장
  Future<bool> setExcelDayOfWeekColumnIndex(int value) async {
    try {
      LoggerUtils.logInfo('요일 열 인덱스 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('excel_day_of_week_column_index', value);
      LoggerUtils.logInfo('요일 열 인덱스 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set excel day of week column index',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 엑셀 요일 열 인덱스 조회
  Future<int?> getExcelDayOfWeekColumnIndex() async {
    try {
      LoggerUtils.logInfo('요일 열 인덱스 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('excel_day_of_week_column_index');
      LoggerUtils.logInfo('요일 열 인덱스 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get excel day of week column index',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 선입금 재고 연동 설정 저장
  Future<bool> setLinkPrepaymentToInventory(bool value) async {
    try {
      LoggerUtils.logInfo('재고 연동 설정 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool('link_prepayment_to_inventory', value);
      LoggerUtils.logInfo('재고 연동 설정 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set link prepayment to inventory',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 선입금 재고 연동 설정 조회
  Future<bool?> getLinkPrepaymentToInventory() async {
    try {
      LoggerUtils.logInfo('재고 연동 설정 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getBool('link_prepayment_to_inventory');
      LoggerUtils.logInfo('재고 연동 설정 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get link prepayment to inventory',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // UI 열 수 설정 관련 메서드들

  // 재고현황 열 수 저장
  Future<bool> setInventoryColumns(int value) async {
    try {
      LoggerUtils.logInfo('재고현황 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('inventory_columns', value);
      LoggerUtils.logInfo('재고현황 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set inventory columns',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 재고현황 열 수 조회
  Future<int?> getInventoryColumns() async {
    try {
      LoggerUtils.logInfo('재고현황 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('inventory_columns');
      LoggerUtils.logInfo('재고현황 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get inventory columns',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 열 수 저장
  Future<bool> setSaleColumns(int value) async {
    try {
      LoggerUtils.logInfo('판매 화면 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('sale_columns', value);
      LoggerUtils.logInfo('판매 화면 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set sale columns',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 열 수 조회
  Future<int?> getSaleColumns() async {
    try {
      LoggerUtils.logInfo('판매 화면 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('sale_columns');
      LoggerUtils.logInfo('판매 화면 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get sale columns',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 재고현황 세로모드 열 수 저장
  Future<bool> setInventoryColumnsPortrait(int value) async {
    try {
      LoggerUtils.logInfo('재고현황 세로모드 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('inventory_columns_portrait', value);
      LoggerUtils.logInfo('재고현황 세로모드 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set inventory columns portrait',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 재고현황 세로모드 열 수 조회
  Future<int?> getInventoryColumnsPortrait() async {
    try {
      LoggerUtils.logInfo('재고현황 세로모드 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('inventory_columns_portrait');
      LoggerUtils.logInfo('재고현황 세로모드 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get inventory columns portrait',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 재고현황 가로모드 열 수 저장
  Future<bool> setInventoryColumnsLandscape(int value) async {
    try {
      LoggerUtils.logInfo('재고현황 가로모드 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('inventory_columns_landscape', value);
      LoggerUtils.logInfo('재고현황 가로모드 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set inventory columns landscape',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 재고현황 가로모드 열 수 조회
  Future<int?> getInventoryColumnsLandscape() async {
    try {
      LoggerUtils.logInfo('재고현황 가로모드 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('inventory_columns_landscape');
      LoggerUtils.logInfo('재고현황 가로모드 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get inventory columns landscape',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 세로모드 열 수 저장
  Future<bool> setSaleColumnsPortrait(int value) async {
    try {
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('sale_columns_portrait', value);
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set sale columns portrait',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 세로모드 열 수 조회
  Future<int?> getSaleColumnsPortrait() async {
    try {
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('sale_columns_portrait');
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get sale columns portrait',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 가로모드 열 수 저장
  Future<bool> setSaleColumnsLandscape(int value) async {
    try {
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 저장 시작: $value', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt('sale_columns_landscape', value);
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 저장 완료: $value, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set sale columns landscape',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // 판매 화면 가로모드 열 수 조회
  Future<int?> getSaleColumnsLandscape() async {
    try {
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 조회 시작', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getInt('sale_columns_landscape');
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 조회 완료: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get sale columns landscape',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  // ===== 정렬/필터 설정 저장 관련 메서드들 =====

  /// 페이지별 정렬 옵션 저장
  Future<bool> setPageSortOption(String pageKey, String sortOption) async {
    try {
      LoggerUtils.logInfo('페이지 정렬 옵션 저장 시작: $pageKey = $sortOption', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString('sort_${pageKey}', sortOption);
      LoggerUtils.logInfo('페이지 정렬 옵션 저장 완료: $pageKey = $sortOption, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set page sort option',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  /// 페이지별 정렬 옵션 조회
  Future<String?> getPageSortOption(String pageKey) async {
    try {
      LoggerUtils.logInfo('페이지 정렬 옵션 조회 시작: $pageKey', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getString('sort_${pageKey}');
      LoggerUtils.logInfo('페이지 정렬 옵션 조회 완료: $pageKey = $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get page sort option',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  /// 페이지별 필터 설정 저장 (JSON 형태)
  Future<bool> setPageFilterSettings(String pageKey, String filterJson) async {
    try {
      LoggerUtils.logInfo('페이지 필터 설정 저장 시작: $pageKey', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString('filter_${pageKey}', filterJson);
      LoggerUtils.logInfo('페이지 필터 설정 저장 완료: $pageKey, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set page filter settings',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  /// 페이지별 필터 설정 조회 (JSON 형태)
  Future<String?> getPageFilterSettings(String pageKey) async {
    try {
      LoggerUtils.logInfo('페이지 필터 설정 조회 시작: $pageKey', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getString('filter_${pageKey}');
      LoggerUtils.logInfo('페이지 필터 설정 조회 완료: $pageKey', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get page filter settings',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  /// 페이지별 검색어 저장
  Future<bool> setPageSearchQuery(String pageKey, String searchQuery) async {
    try {
      LoggerUtils.logInfo('페이지 검색어 저장 시작: $pageKey = $searchQuery', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString('search_${pageKey}', searchQuery);
      LoggerUtils.logInfo('페이지 검색어 저장 완료: $pageKey = $searchQuery, 결과: $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to set page search query',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }

  /// 페이지별 검색어 조회
  Future<String?> getPageSearchQuery(String pageKey) async {
    try {
      LoggerUtils.logInfo('페이지 검색어 조회 시작: $pageKey', tag: 'SettingsRepository');
      final prefs = await SharedPreferences.getInstance();
      final result = prefs.getString('search_${pageKey}');
      LoggerUtils.logInfo('페이지 검색어 조회 완료: $pageKey = $result', tag: 'SettingsRepository');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get page search query',
        error: e,
        stackTrace: stackTrace,
        tag: 'SettingsRepository',
      );
      rethrow;
    }
  }


}
