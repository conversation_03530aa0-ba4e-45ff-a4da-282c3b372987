import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

/// 튜토리얼 섹션 데이터 모델
class TutorialSection {
  final String title;
  final List<String> steps;

  const TutorialSection({
    required this.title,
    required this.steps,
  });

  TutorialSection copyWith({
    String? title,
    List<String>? steps,
  }) {
    return TutorialSection(
      title: title ?? this.title,
      steps: steps ?? this.steps,
    );
  }
}

/// 튜토리얼 다이얼로그 - 유튜브 영상과 텍스트 설명 포함
class TutorialDialog extends StatefulWidget {
  final String title;
  final String? youtubeVideoId;
  final String description;
  final bool showVideo;

  const TutorialDialog({
    super.key,
    required this.title,
    this.youtubeVideoId,
    required this.description,
    this.showVideo = true,
  });

  @override
  State<TutorialDialog> createState() => _TutorialDialogState();

  /// 튜토리얼 다이얼로그 표시
  static Future<void> show({
    required BuildContext context,
    required String title,
    String? youtubeVideoId,
    required String description,
    bool showVideo = true,
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) => TutorialDialog(
        title: title,
        youtubeVideoId: youtubeVideoId,
        description: description,
        showVideo: showVideo,
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        // 페이드 인 + 아래에서 위로 슬라이드 애니메이션
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.3), // 아래에서 시작
              end: Offset.zero, // 원래 위치로
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
    );
  }
}

class _TutorialDialogState extends State<TutorialDialog> {
  YoutubePlayerController? _controller;
  bool _isPlayerReady = false;
  bool _isPlayerInitialized = false;

  void _initializePlayer() {
    if (!_isPlayerInitialized && widget.showVideo && widget.youtubeVideoId != null) {
      _controller = YoutubePlayerController(
        initialVideoId: widget.youtubeVideoId!,
        flags: const YoutubePlayerFlags(
          mute: false,
          autoPlay: false,
          disableDragSeek: false,
          loop: false,
          isLive: false,
          forceHD: false,
          enableCaption: true,
        ),
      );
      _controller!.addListener(_listener);
      _isPlayerInitialized = true;
      setState(() {});
    }
  }

  void _listener() {
    if (_isPlayerReady && mounted && _controller != null && _controller!.value.isReady) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    if (_controller != null) {
      _controller!.removeListener(_listener);
      _controller!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // 다이얼로그 크기 계산 (거의 화면 전체)
    final dialogWidth = size.width * (isLandscape ? 0.9 : 0.95);
    final dialogHeight = size.height * (isLandscape ? 0.9 : 0.9);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(isLandscape ? 8 : 16),
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            // 헤더
            Container(
              padding: EdgeInsets.all(isTablet ? 20 : 16),
              decoration: BoxDecoration(
                color: AppColors.primarySeed,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: isTablet ? 28 : 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            // 내용 (스크롤 가능)
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isTablet ? 24 : 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 유튜브 플레이어 (showVideo가 true이고 youtubeVideoId가 있을 때만)
                    if (widget.showVideo && widget.youtubeVideoId != null) ...[
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _isPlayerInitialized && _controller != null
                              ? YoutubePlayerBuilder(
                                  onExitFullScreen: () {
                                    // 전체화면 종료 시 처리
                                  },
                                  player: YoutubePlayer(
                                    controller: _controller!,
                                    showVideoProgressIndicator: true,
                                    onReady: () {
                                      _isPlayerReady = true;
                                    },
                                    onEnded: (data) {
                                      // 영상 종료 시 처리
                                    },
                                  ),
                                  builder: (context, player) => player,
                                )
                              : Container(
                                  height: 200,
                                  color: Colors.black12,
                                  child: Center(
                                    child: ElevatedButton.icon(
                                      onPressed: _initializePlayer,
                                      icon: const Icon(Icons.play_arrow),
                                      label: const Text('동영상 재생'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppColors.primarySeed,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      SizedBox(height: isTablet ? 32 : 24),
                    ],

                    // 설명 텍스트 (원본 그대로, 스타일링만 개선)
                    Text(
                      '사용 방법',
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                        fontFamily: 'Pretendard',
                      ),
                    ),

                    SizedBox(height: isTablet ? 16 : 12),

                    // 구조화된 설명 콘텐츠
                    _buildDescriptionContent(context),

                    SizedBox(height: isTablet ? 32 : 24),

                    // 하단 버튼
                    Center(
                      child: custom_dialog.DialogTheme.buildModernButton(
                        text: '확인',
                        onPressed: () => Navigator.of(context).pop(),
                        isTablet: isTablet,
                        isPrimary: true,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 구조화된 설명 콘텐츠 빌드
  Widget _buildDescriptionContent(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    // 텍스트를 섹션별로 파싱
    final sections = _parseDescriptionSections(widget.description);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: sections.map((section) => _buildSection(context, section, isTablet)).toList(),
    );
  }

  /// 설명 텍스트를 섹션별로 파싱
  List<TutorialSection> _parseDescriptionSections(String description) {
    final sections = <TutorialSection>[];
    final lines = description.split('\n');

    TutorialSection? currentSection;
    final List<String> currentSteps = [];

    for (final line in lines) {
      final trimmedLine = line.trim();

      if (trimmedLine.isEmpty) continue;

      // 섹션 제목 감지 (콜론으로 끝나는 줄)
      if (trimmedLine.endsWith(':') && !trimmedLine.startsWith('•') && !trimmedLine.startsWith('-')) {
        // 이전 섹션 저장
        if (currentSection != null) {
          sections.add(currentSection.copyWith(steps: List.from(currentSteps)));
          currentSteps.clear();
        }

        // 새 섹션 시작
        currentSection = TutorialSection(
          title: trimmedLine.substring(0, trimmedLine.length - 1),
          steps: [],
        );
      }
      // 불릿 포인트나 단계 감지
      else if (trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || RegExp(r'^\d+\.').hasMatch(trimmedLine)) {
        String stepText = trimmedLine;
        if (stepText.startsWith('•') || stepText.startsWith('-')) {
          stepText = stepText.substring(1).trim();
        }
        currentSteps.add(stepText);
      }
      // 일반 텍스트
      else {
        if (currentSection == null) {
          // 첫 번째 섹션이 없으면 "개요" 섹션으로 생성
          currentSection = TutorialSection(
            title: '개요',
            steps: [],
          );
        }
        currentSteps.add(trimmedLine);
      }
    }

    // 마지막 섹션 저장
    if (currentSection != null) {
      sections.add(currentSection.copyWith(steps: List.from(currentSteps)));
    }

    return sections;
  }

  /// 개별 섹션 빌드
  Widget _buildSection(BuildContext context, TutorialSection section, bool isTablet) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1.withValues(alpha: 0.5),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 섹션 헤더
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primarySeed.withValues(alpha: 0.1),
                  AppColors.primarySeed.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primarySeed.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.lightbulb_outline,
                    color: AppColors.primarySeed,
                    size: isTablet ? 20 : 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    section.title,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: isTablet ? 18.0 : 16.0,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primarySeed,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 섹션 내용
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: section.steps.map((step) => _buildStep(context, step, isTablet)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 개별 단계 빌드
  Widget _buildStep(BuildContext context, String step, bool isTablet) {
    final trimmedStep = step.trim();

    // 숫자로 시작하는지 확인 (예: "1. ", "2. ")
    final isNumberedStep = RegExp(r'^\d+\.\s').hasMatch(trimmedStep);

    // 아이콘이 포함되어 있는지 확인
    final hasIcon = RegExp(r'(folder_outlined|local_offer|money_off)\s*아이콘').hasMatch(trimmedStep);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 불릿 포인트 (숫자 단계나 아이콘이 포함된 단계가 아닐 때만)
          if (!isNumberedStep && !hasIcon) ...[
            Container(
              margin: const EdgeInsets.only(top: 6),
              child: Icon(
                Icons.fiber_manual_record,
                color: AppColors.primarySeed,
                size: isTablet ? 8 : 6,
              ),
            ),
            const SizedBox(width: 12),
          ],

          // 단계 텍스트 (아이콘 포함)
          Expanded(
            child: _buildStepText(trimmedStep, isTablet, isNumberedStep),
          ),
        ],
      ),
    );
  }

  /// 단계 텍스트 생성 (아이콘 변환 포함)
  Widget _buildStepText(String text, bool isTablet, bool isNumberedStep) {
    // 아이콘 매핑
    final iconMap = {
      'folder_outlined': Icons.folder_outlined,
      'local_offer': Icons.local_offer,
      'money_off': Icons.money_off,
    };

    // 아이콘 패턴 감지 (예: "folder_outlined 아이콘")
    final iconPattern = RegExp(r'(folder_outlined|local_offer|money_off)\s*아이콘');

    if (iconPattern.hasMatch(text)) {
      final spans = <InlineSpan>[];
      int lastEnd = 0;

      for (final match in iconPattern.allMatches(text)) {
        // 매치 이전 텍스트 추가
        if (match.start > lastEnd) {
          final beforeText = text.substring(lastEnd, match.start);
          spans.add(_createTextSpan(beforeText, isTablet, isNumberedStep));
        }

        // 아이콘 추가
        final iconName = match.group(1)!;
        final icon = iconMap[iconName];
        if (icon != null) {
          spans.add(WidgetSpan(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Icon(
                icon,
                size: isTablet ? 18 : 16,
                color: AppColors.primarySeed,
              ),
            ),
            alignment: PlaceholderAlignment.middle,
          ));
        }

        lastEnd = match.end;
      }

      // 매치 이후 텍스트 추가
      if (lastEnd < text.length) {
        final afterText = text.substring(lastEnd);
        spans.add(_createTextSpan(afterText, isTablet, isNumberedStep));
      }

      return Text.rich(
        TextSpan(children: spans),
      );
    } else {
      // 아이콘이 없는 경우 일반 텍스트
      return Text.rich(
        _createTextSpan(text, isTablet, isNumberedStep),
      );
    }
  }

  /// 텍스트 스팬 생성 (숫자 강조 포함)
  TextSpan _createTextSpan(String text, bool isTablet, bool isNumberedStep) {
    if (isNumberedStep) {
      // 숫자 부분과 나머지 분리
      final numberMatch = RegExp(r'^(\d+\.\s)(.*)').firstMatch(text);
      if (numberMatch != null) {
        final numberPart = numberMatch.group(1)!;
        final restPart = numberMatch.group(2)!;

        return TextSpan(
          children: [
            TextSpan(
              text: numberPart,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 15.0 : 14.0,
                height: 1.6,
                color: AppColors.primarySeed,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(
              text: restPart,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 15.0 : 14.0,
                height: 1.6,
                color: AppColors.onSurface,
              ),
            ),
          ],
        );
      }
    }

    // 일반 텍스트
    return TextSpan(
      text: text,
      style: TextStyle(
        fontFamily: 'Pretendard',
        fontSize: isTablet ? 15.0 : 14.0,
        height: 1.6,
        color: AppColors.onSurface,
      ),
    );
  }
}
